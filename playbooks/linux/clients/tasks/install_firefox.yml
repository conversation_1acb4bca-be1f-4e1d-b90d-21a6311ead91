---
# Install Firefox using Mozilla's official .deb package for Debian-based distributions
# Based on: https://support.mozilla.org/en-US/kb/install-firefox-linux#w_install-firefox-deb-package-for-debian-based-distributions-recommended
#
# This task installs Firefox using Mozilla's official APT repository which provides:
# - Latest Firefox releases directly from Mozilla
# - Automatic updates through APT
# - Better integration than Snap/Flatpak
# - Support for multiple Firefox channels (stable, ESR, beta, nightly, dev edition)

- name: Install Firefox - Check if running on Debian-based system
  ansible.builtin.fail:
    msg: "This task only supports Debian-based distributions (Ubuntu, Debian, etc.)"
  when: ansible_facts['os_family'] | lower != 'debian'
  tags: ['firefox', 'validation']

- name: Install Firefox - Install wget if not present
  ansible.builtin.apt:
    name: wget
    state: present
    update_cache: true
  tags: ['firefox', 'dependencies']

- name: Install Firefox - Create APT keyrings directory
  ansible.builtin.file:
    path: /etc/apt/keyrings
    state: directory
    mode: '0755'
    owner: root
    group: root
  tags: ['firefox', 'setup']

- name: Install Firefox - Download Mozilla APT repository signing key
  ansible.builtin.get_url:
    url: https://packages.mozilla.org/apt/repo-signing-key.gpg
    dest: /etc/apt/keyrings/packages.mozilla.org.asc
    mode: '0644'
    owner: root
    group: root
  tags: ['firefox', 'setup']

- name: Install Firefox - Verify GPG key fingerprint
  ansible.builtin.shell: |
    gpg -n -q --import --import-options import-show /etc/apt/keyrings/packages.mozilla.org.asc | \
    awk '/pub/{getline; gsub(/^ +| +$/,""); if($0 == "35BAA0B33E9EB396F59CA838C0BA5CE6DC6315A3") print "MATCH"; else print "MISMATCH"}'
  register: firefox_gpg_check
  changed_when: false
  tags: ['firefox', 'verification']

- name: Install Firefox - Fail if GPG key fingerprint doesn't match
  ansible.builtin.fail:
    msg: "GPG key fingerprint verification failed. Expected: 35BAA0B33E9EB396F59CA838C0BA5CE6DC6315A3"
  when: firefox_gpg_check.stdout != "MATCH"
  tags: ['firefox', 'verification']

- name: Install Firefox - Add Mozilla APT repository
  ansible.builtin.apt_repository:
    repo: "deb [signed-by=/etc/apt/keyrings/packages.mozilla.org.asc] https://packages.mozilla.org/apt mozilla main"
    filename: mozilla
    state: present
  tags: ['firefox', 'setup']

- name: Install Firefox - Configure APT to prioritize Mozilla repository
  ansible.builtin.copy:
    content: |
      Package: *
      Pin: origin packages.mozilla.org
      Pin-Priority: 1000
    dest: /etc/apt/preferences.d/mozilla
    mode: '0644'
    owner: root
    group: root
  tags: ['firefox', 'setup']

- name: Install Firefox - Update APT cache
  ansible.builtin.apt:
    update_cache: true
  tags: ['firefox', 'setup']

- name: Install Firefox - Install Firefox from Mozilla repository
  ansible.builtin.apt:
    name: "{{ firefox_package | default('firefox') }}"
    state: present
    update_cache: false
  tags: ['firefox', 'install']

- name: Install Firefox - Install language pack (if specified)
  ansible.builtin.apt:
    name: "firefox-l10n-{{ firefox_language }}"
    state: present
    update_cache: false
  when: firefox_language is defined and firefox_language != 'en-US'
  tags: ['firefox', 'language']

- name: Install Firefox - Verify installation
  ansible.builtin.command: firefox --version
  register: firefox_version_check
  changed_when: false
  tags: ['firefox', 'verification']

- name: Install Firefox - Display installation result
  ansible.builtin.debug:
    msg: |
      Firefox installation completed successfully!
      
      Installed version: {{ firefox_version_check.stdout }}
      Package: {{ firefox_package | default('firefox') }}
      {% if firefox_language is defined and firefox_language != 'en-US' %}
      Language pack: firefox-l10n-{{ firefox_language }}
      {% endif %}
      
      Firefox is now installed from Mozilla's official repository and will receive
      automatic updates through APT.
      
      Available Firefox packages:
      - firefox (stable release)
      - firefox-esr (Extended Support Release)
      - firefox-beta (beta channel)
      - firefox-nightly (nightly builds)
      - firefox-devedition (developer edition)
      
      To install a different channel, set firefox_package variable:
      firefox_package: firefox-esr
  tags: ['firefox', 'completion']
