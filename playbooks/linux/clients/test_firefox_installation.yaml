---
# Firefox Installation Test Playbook
# 
# This playbook tests Firefox installation and configuration
#
# Usage:
#   ansible-playbook -i hosts.yaml test_firefox_installation.yaml -e "hosts=linux_clients" -u aule -b

- name: Test Firefox Installation
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: false
  remote_user: aule

  tasks:
    - name: Check if Firefox is installed
      ansible.builtin.command: which firefox
      register: firefox_which
      changed_when: false
      failed_when: false
      tags: ['test']

    - name: Get Firefox version
      ansible.builtin.command: firefox --version
      register: firefox_version
      changed_when: false
      failed_when: false
      when: firefox_which.rc == 0
      tags: ['test']

    - name: Check Mozilla APT repository
      ansible.builtin.stat:
        path: /etc/apt/sources.list.d/mozilla.list
      register: mozilla_repo
      tags: ['test']

    - name: Check Mozilla GPG key
      ansible.builtin.stat:
        path: /etc/apt/keyrings/packages.mozilla.org.asc
      register: mozilla_key
      tags: ['test']

    - name: Check APT preferences for Mozilla
      ansible.builtin.stat:
        path: /etc/apt/preferences.d/mozilla
      register: mozilla_preferences
      tags: ['test']

    - name: Display test results
      ansible.builtin.debug:
        msg: |
          Firefox Installation Test Results:
          
          Firefox executable: {{ 'Found' if firefox_which.rc == 0 else 'Not found' }}
          {% if firefox_which.rc == 0 %}
          Firefox version: {{ firefox_version.stdout }}
          {% endif %}
          Mozilla APT repository: {{ 'Configured' if mozilla_repo.stat.exists else 'Not configured' }}
          Mozilla GPG key: {{ 'Present' if mozilla_key.stat.exists else 'Missing' }}
          APT preferences: {{ 'Configured' if mozilla_preferences.stat.exists else 'Not configured' }}
          
          {% if firefox_which.rc == 0 and mozilla_repo.stat.exists and mozilla_key.stat.exists %}
          ✅ Firefox installation appears to be working correctly!
          {% else %}
          ❌ Firefox installation has issues. Please check the configuration.
          {% endif %}
      tags: ['results']
