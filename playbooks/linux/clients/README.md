# Linux Client Setup

This directory contains Ansible playbooks for setting up Linux client machines with standardized configurations.

## Overview

The `linux_client_setup.yaml` playbook automates the setup of Linux client machines with:

- **User Management**: Creates automation user `aule` and primary user `krizzo` (UID 1985)
- **Package Installation**: Installs essential packages for development and system administration
- **Shell Configuration**: Sets up zsh as default shell for krizzo with neovim as default editor
- **SSH Configuration**: Configures SSH access with public key authentication
- **System Configuration**: Sets up basic system settings and services
- **Optional Software**: Firefox installation from Mozilla's official repository (Debian-based systems)

## Files

- `linux_client_setup.yaml` - Main playbook for Linux client setup
- `install_firefox_only.yaml` - Standalone Firefox installation playbook
- `test_firefox_installation.yaml` - Firefox installation test playbook
- `hosts.yaml` - Inventory file for target hosts
- `ansible.cfg` - Ansible configuration
- `requirements.yaml` - Required Ansible collections
- `tasks/create_user.yml` - User creation task
- `tasks/install_firefox.yml` - Firefox installation task

## Users Created

### aule (Automation User)
- **UID**: 1000
- **Shell**: /bin/bash
- **Purpose**: Used for Ansible automation and system management
- **Groups**: sudo
- **SSH**: Disabled (password/key authentication as needed)

### krizzo (Primary User)
- **UID**: 1985
- **Shell**: /usr/bin/zsh
- **Purpose**: Primary user account for daily use
- **Groups**: sudo, users
- **SSH**: Enabled with public key authentication
- **Environment**: 
  - EDITOR=nvim
  - LANG=en_US.UTF-8

## Package List

### Common Packages (Both Debian and RHEL)
- **System Tools**: sudo, curl, wget, git, neovim, zsh, tmux, htop, tree, rsync
- **Development**: build-essential (Debian) / Development Tools (RHEL), python3, python3-pip
- **Network Tools**: ssh, openssh-client/server, dnsutils/bind-utils, net-tools, tcpdump, mtr
- **Security**: iptables, ufw (Debian) / firewalld (RHEL), fail2ban
- **Utilities**: unzip, zip, jq, bat, fd-find, ripgrep, fzf, ncdu, duf, tldr
- **Multimedia**: ksnip (screenshot tool), simplescreenrecorder (screen recording)

### Debian-Specific
- apt-transport-https, ca-certificates, gnupg, lsb-release, software-properties-common

### RHEL-Specific  
- epel-release, firewalld

## Usage

### Prerequisites

1. **Target System Requirements**:
   - Fresh Linux installation (Debian/Ubuntu or RHEL/Rocky/CentOS/Fedora)
   - User `aule` exists with sudo privileges
   - SSH access configured for user `aule`
   - Python3 installed (for Ansible)

2. **Control Machine Requirements**:
   - Ansible installed
   - SSH access to target machines
   - ansible.posix collection installed: `ansible-galaxy collection install ansible.posix`

### Basic Setup

1. **Configure Inventory**:
   Edit `hosts.yaml` to add your target machines:
   ```yaml
   workstations:
     hosts:
       my-workstation:
         ansible_host: *************
         ansible_user: aule
   ```

2. **Run the Playbook**:
   ```bash
   # Setup all Linux clients
   ansible-playbook linux_client_setup.yaml
   
   # Setup specific group
   ansible-playbook linux_client_setup.yaml -e "hosts=workstations"
   
   # Setup single host
   ansible-playbook linux_client_setup.yaml -e "hosts=my-workstation"

   # Setup with Firefox installation
   ansible-playbook linux_client_setup.yaml -e "install_firefox=true"
   ```

3. **With Authentication**:
   ```bash
   # If password authentication is needed
   ansible-playbook linux_client_setup.yaml -k -K
   
   # If only sudo password is needed
   ansible-playbook linux_client_setup.yaml -K
   ```

### Firefox Installation

The playbook includes optional Firefox installation using Mozilla's recommended method for Debian-based distributions:

#### Basic Firefox Installation
```bash
# Install Firefox stable release
ansible-playbook linux_client_setup.yaml -e "install_firefox=true"

# Install Firefox ESR (Extended Support Release)
ansible-playbook linux_client_setup.yaml -e "install_firefox=true firefox_package=firefox-esr"

# Install Firefox with language pack
ansible-playbook linux_client_setup.yaml -e "install_firefox=true firefox_language=fr"
```

#### Firefox Installation Options
- `install_firefox` - Set to `true` to enable Firefox installation (default: `false`)
- `firefox_package` - Firefox package to install (default: `firefox`)
  - `firefox` - Stable release
  - `firefox-esr` - Extended Support Release
  - `firefox-beta` - Beta channel
  - `firefox-nightly` - Nightly builds
  - `firefox-devedition` - Developer edition
- `firefox_language` - Language code for language pack (e.g., `fr`, `de`, `es`)

#### Firefox-Only Installation
```bash
# Only install Firefox, skip other setup phases
ansible-playbook linux_client_setup.yaml -e "install_firefox=true" --tags firefox

# Use standalone Firefox installation playbook
ansible-playbook install_firefox_only.yaml
```

#### Testing Firefox Installation
```bash
# Test Firefox installation
ansible-playbook test_firefox_installation.yaml
```

**Note**: Firefox installation is only supported on Debian-based distributions (Ubuntu, Debian, etc.)

### Advanced Usage

#### Custom Package Lists
You can override the default package list by defining variables:

```bash
ansible-playbook linux_client_setup.yaml -e "linux_client_packages={'debian': ['custom-package-1', 'custom-package-2']}"
```

#### Skip Phases
Use tags to run specific phases:

```bash
# Only install packages
ansible-playbook linux_client_setup.yaml --tags packages

# Only create users
ansible-playbook linux_client_setup.yaml --tags users

# Skip user creation
ansible-playbook linux_client_setup.yaml --skip-tags users
```

#### Dry Run
Test the playbook without making changes:

```bash
ansible-playbook linux_client_setup.yaml --check --diff
```

## Verification

After running the playbook, verify the setup:

1. **Test SSH Access**:
   ```bash
   ssh krizzo@target-host
   ```

2. **Verify User Configuration**:
   ```bash
   # Check user exists with correct UID
   id krizzo
   
   # Check shell
   echo $SHELL
   
   # Check default editor
   echo $EDITOR
   ```

3. **Verify Package Installation**:
   ```bash
   # Debian/Ubuntu
   dpkg -l | grep neovim
   
   # RHEL/Rocky/CentOS
   rpm -qa | grep neovim
   ```

## Customization

### Adding Custom Users
Modify the `client_users` variable in the playbook to add additional users:

```yaml
client_users:
  - name: "custom_user"
    id: "2000"
    shell: "/bin/bash"
    create_home: true
    groups: ["users"]
```

### Adding Custom Packages
Extend the package lists in the playbook variables:

```yaml
linux_client_packages:
  debian:
    - existing-packages...
    - custom-package-1
    - custom-package-2
```

### Environment Variables
Add custom environment variables for users:

```yaml
client_users:
  - name: "krizzo"
    environment:
      EDITOR: "nvim"
      CUSTOM_VAR: "custom_value"
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**:
   - Verify user `aule` exists on target system
   - Check SSH service is running
   - Verify network connectivity

2. **Permission Denied**:
   - Ensure user `aule` has sudo privileges
   - Check sudoers configuration

3. **Package Installation Failed**:
   - Verify internet connectivity on target system
   - Check package repositories are accessible
   - Review package names for your distribution

### Debug Mode
Run with increased verbosity for troubleshooting:

```bash
ansible-playbook linux_client_setup.yaml -vvv
```

## Security Considerations

- The playbook creates users with sudo access
- SSH keys are configured for secure access
- Default passwords should be changed after initial setup
- Consider implementing additional security hardening as needed

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Ansible logs for detailed error messages
3. Verify target system meets prerequisites
4. Test connectivity with `ansible all -m ping`
