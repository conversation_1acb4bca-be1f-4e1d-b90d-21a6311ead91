---
# Firefox Installation Playbook
# 
# This playbook installs Firefox using Mozilla's official .deb package method
# for Debian-based distributions (Ubuntu, Debian, etc.)
#
# Based on: https://support.mozilla.org/en-US/kb/install-firefox-linux#w_install-firefox-deb-package-for-debian-based-distributions-recommended
#
# Usage:
#   ansible-playbook -i hosts.yaml install_firefox_only.yaml -e "hosts=linux_clients" -u aule -b
#
# Firefox installation options:
#   -e "firefox_package=firefox-esr"     # Install ESR version
#   -e "firefox_language=fr"             # Install French language pack

- name: Install Firefox on Linux Clients
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: true
  remote_user: aule

  vars:
    # Firefox installation is enabled by default in this playbook
    install_firefox: true

  tasks:
    - name: Display system information
      ansible.builtin.debug:
        msg: |
          Installing Firefox on:
          - OS Family: {{ ansible_facts['os_family'] }}
          - Distribution: {{ ansible_facts['distribution'] }}
          - Distribution Version: {{ ansible_facts['distribution_version'] }}
          - Architecture: {{ ansible_facts['architecture'] }}
      tags: ['info']

    - name: Install Firefox
      ansible.builtin.include_tasks: tasks/install_firefox.yml
      tags: ['firefox']

    - name: Installation complete
      ansible.builtin.debug:
        msg: |
          Firefox installation completed successfully!
          
          Package installed: {{ firefox_package | default('firefox') }}
          {% if firefox_language is defined and firefox_language != 'en-US' %}
          Language pack: firefox-l10n-{{ firefox_language }}
          {% endif %}
          
          You can now launch Firefox with: firefox
          
          Firefox will receive automatic updates through APT from Mozilla's official repository.
      tags: ['completion']
