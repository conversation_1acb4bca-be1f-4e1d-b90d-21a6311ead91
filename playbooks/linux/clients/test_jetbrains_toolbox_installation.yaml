---
# JetBrains Toolbox Installation Test Playbook
#
# This playbook tests the JetBrains Toolbox installation by:
# - Verifying all required dependencies are installed
# - Checking if the binary exists and is executable
# - Validating desktop integration
# - Testing basic functionality
#
# Usage:
#   ansible-playbook -i hosts.yaml test_jetbrains_toolbox_installation.yaml -e "hosts=linux_clients" -u aule -b

- name: Test JetBrains Toolbox Installation
  hosts: "{{ hosts | default('linux_clients') }}"
  gather_facts: true
  become: true
  remote_user: aule

  vars:
    # Test user (should match the installation user)
    test_user: "{{ jetbrains_toolbox_user | default('krizzo') }}"

  tasks:
    - name: Display test information
      ansible.builtin.debug:
        msg: |
          Testing JetBrains Toolbox Installation:
          - Target User: {{ test_user }}
          - OS: {{ ansible_facts['distribution'] }} {{ ansible_facts['distribution_version'] }}
          - Architecture: {{ ansible_facts['architecture'] }}
      tags: ['info']

    - name: Test - Verify target user exists
      ansible.builtin.getent:
        database: passwd
        key: "{{ test_user }}"
      register: user_check
      tags: ['user']

    - name: Test - Get user home directory
      ansible.builtin.set_fact:
        user_home: "{{ user_check.ansible_facts.getent_passwd[test_user][4] }}"
      tags: ['user']

    - name: Test - Check required dependencies (Debian)
      ansible.builtin.package_facts:
        manager: apt
      when: ansible_facts['os_family'] | lower == 'debian'
      tags: ['dependencies']

    - name: Test - Check required dependencies (RHEL)
      ansible.builtin.package_facts:
        manager: dnf
      when: ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora']
      tags: ['dependencies']

    - name: Test - Verify JetBrains Toolbox binary exists
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
      register: binary_check
      tags: ['binary']

    - name: Test - Verify binary is executable
      ansible.builtin.file:
        path: "{{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
        mode: '0755'
        state: file
      when: binary_check.stat.exists
      tags: ['binary']

    - name: Test - Check PATH symlink exists
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/bin/jetbrains-toolbox"
      register: symlink_check
      tags: ['symlink']

    - name: Test - Verify desktop entry exists
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/share/applications/jetbrains-toolbox.desktop"
      register: desktop_check
      tags: ['desktop']

    - name: Test - Validate desktop entry content
      ansible.builtin.slurp:
        src: "{{ user_home }}/.local/share/applications/jetbrains-toolbox.desktop"
      register: desktop_content
      when: desktop_check.stat.exists
      tags: ['desktop']

    - name: Test - Check directory permissions
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/share/JetBrains/Toolbox"
      register: dir_permissions
      tags: ['permissions']

    - name: Test - Verify ownership
      ansible.builtin.stat:
        path: "{{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox"
      register: ownership_check
      tags: ['permissions']

    - name: Test - Try to get version (as user)
      ansible.builtin.shell: |
        su - {{ test_user }} -c "{{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox --version 2>/dev/null || echo 'Version check failed'"
      register: version_check
      changed_when: false
      failed_when: false
      tags: ['functionality']

    - name: Test - Display comprehensive test results
      ansible.builtin.debug:
        msg: |
          JetBrains Toolbox Installation Test Results:
          ============================================
          
          User Information:
          - Test User: {{ test_user }}
          - User Home: {{ user_home }}
          - User Exists: {{ user_check.ansible_facts.getent_passwd[test_user] is defined }}
          
          Binary Tests:
          - Binary Exists: {{ binary_check.stat.exists | default(false) }}
          - Binary Executable: {{ binary_check.stat.executable | default(false) }}
          - Binary Size: {{ binary_check.stat.size | default(0) }} bytes
          - Binary Owner: {{ ownership_check.stat.pw_name | default('unknown') }}
          
          Integration Tests:
          - PATH Symlink: {{ symlink_check.stat.exists | default(false) }}
          - Desktop Entry: {{ desktop_check.stat.exists | default(false) }}
          - Directory Permissions: {{ dir_permissions.stat.mode | default('unknown') }}
          
          Functionality Tests:
          - Version Check: {{ version_check.stdout | default('Failed') }}
          - Return Code: {{ version_check.rc | default(-1) }}
          
          Dependencies (Sample):
          {% if ansible_facts['os_family'] | lower == 'debian' %}
          - libfuse2: {{ 'Installed' if 'libfuse2' in ansible_facts.packages else 'Missing' }}
          - libxi6: {{ 'Installed' if 'libxi6' in ansible_facts.packages else 'Missing' }}
          - libgtk-3-bin: {{ 'Installed' if 'libgtk-3-bin' in ansible_facts.packages else 'Missing' }}
          {% endif %}
          
          Overall Status: {{ 'PASS' if (binary_check.stat.exists and desktop_check.stat.exists and symlink_check.stat.exists) else 'FAIL' }}
      tags: ['results']

    - name: Test - Summary and recommendations
      ansible.builtin.debug:
        msg: |
          Test Summary:
          {% if binary_check.stat.exists and desktop_check.stat.exists and symlink_check.stat.exists %}
          ✅ JetBrains Toolbox installation appears to be successful!
          
          Next steps for {{ test_user }}:
          1. Login as {{ test_user }}: su - {{ test_user }}
          2. Launch Toolbox: jetbrains-toolbox
          3. Sign in with JetBrains account
          4. Install desired IDEs
          {% else %}
          ❌ JetBrains Toolbox installation has issues:
          
          Issues found:
          {% if not binary_check.stat.exists %}
          - Binary missing: {{ user_home }}/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox
          {% endif %}
          {% if not desktop_check.stat.exists %}
          - Desktop entry missing: {{ user_home }}/.local/share/applications/jetbrains-toolbox.desktop
          {% endif %}
          {% if not symlink_check.stat.exists %}
          - PATH symlink missing: {{ user_home }}/.local/bin/jetbrains-toolbox
          {% endif %}
          
          Recommended actions:
          1. Re-run the installation playbook
          2. Check system requirements and dependencies
          3. Verify user permissions
          4. Check network connectivity during installation
          {% endif %}
      tags: ['summary']
