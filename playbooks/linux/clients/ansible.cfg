[defaults]
nocows = true
inventory = hosts.yaml
remote_user = aule
private_key_file = ~/.ssh/id_aule_automation_ed25519
host_key_checking = false
stdout_callback = yaml
bin_ansible_callbacks = True
remote_tmp = /tmp/.ansible-${USER}
interpreter_python = auto_silent
timeout = 30
gathering = smart
fact_caching = memory
fact_caching_timeout = 86400

[ssh_connection]
pipelining = True
ssh_args = -o ControlMaster=auto -o ControlPersist=60s -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null
control_path_dir = /tmp/.ansible-cp

[privilege_escalation]
become = True
become_method = sudo
become_user = root
become_ask_pass = False
