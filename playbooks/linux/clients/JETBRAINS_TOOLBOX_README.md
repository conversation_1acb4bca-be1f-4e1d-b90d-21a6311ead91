# JetBrains Toolbox Installation for Linux Clients

This directory contains Ansible playbooks and tasks for installing JetBrains Toolbox on Linux client systems.

## Overview

JetBrains Toolbox is a desktop application that allows you to easily install, update, and manage JetBrains IDEs and tools. This installation:

- Downloads the latest version directly from JetBrains API
- Installs all required system dependencies
- Sets up desktop integration
- Configures automatic updates
- Installs for user `krizzo` by default

## Files

- `install_jetbrains_toolbox.yaml` - Main installation playbook
- `tasks/install_jetbrains_toolbox.yml` - Installation task (included in main client setup)
- `test_jetbrains_toolbox_installation.yaml` - Test and verification playbook
- `JETBRAINS_TOOLBOX_README.md` - This documentation

## System Requirements

### Supported Operating Systems
- Ubuntu 18.04 or later (64-bit x86)
- Ubuntu 20.04 or later (arm64)
- Debian-based distributions
- RHEL/CentOS/Rocky/Fedora-based distributions

### Required Dependencies
The playbook automatically installs these dependencies:

**Debian/Ubuntu:**
- libfuse2
- libxi6
- libxrender1
- libxtst6
- mesa-utils
- libfontconfig1
- libgtk-3-bin
- tar
- dbus-user-session
- curl
- wget

**RHEL/CentOS/Fedora:**
- fuse
- libXi
- libXrender
- libXtst
- mesa-utils
- fontconfig
- gtk3
- tar
- curl
- wget

## Usage

### Standalone Installation

Install JetBrains Toolbox only:

```bash
# Install for default user (krizzo)
ansible-playbook -i hosts.yaml install_jetbrains_toolbox.yaml -e "hosts=linux_clients" -u aule -b

# Install for custom user
ansible-playbook -i hosts.yaml install_jetbrains_toolbox.yaml -e "hosts=linux_clients jetbrains_toolbox_user=myuser" -u aule -b
```

### Integrated with Client Setup

Install as part of the main client setup:

```bash
# Include JetBrains Toolbox in client setup
ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=linux_clients install_jetbrains_toolbox=true" -u aule -b

# Custom user for JetBrains Toolbox
ansible-playbook -i hosts.yaml linux_client_setup.yaml -e "hosts=linux_clients install_jetbrains_toolbox=true jetbrains_toolbox_user=developer" -u aule -b
```

### Testing Installation

Verify the installation:

```bash
ansible-playbook -i hosts.yaml test_jetbrains_toolbox_installation.yaml -e "hosts=linux_clients" -u aule -b
```

## Installation Details

### Installation Paths
- **Binary**: `~/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox`
- **Desktop Entry**: `~/.local/share/applications/jetbrains-toolbox.desktop`
- **PATH Symlink**: `~/.local/bin/jetbrains-toolbox`

### What Gets Installed
1. System dependencies for running AppImage applications
2. Latest JetBrains Toolbox binary from official API
3. Desktop menu integration
4. Command-line access via PATH

### User Setup
The installation is performed for a specific user (default: `krizzo`):
- All files are owned by the target user
- Installation is in user's home directory
- No system-wide installation required

## Post-Installation

### First Launch
1. Login as the target user: `su - krizzo`
2. Ensure `~/.local/bin` is in PATH:
   ```bash
   echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```
3. Launch JetBrains Toolbox:
   - Command line: `jetbrains-toolbox`
   - Desktop menu: Search for "JetBrains Toolbox"
   - Direct path: `~/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox`

### JetBrains Account
1. Sign in with your JetBrains account
2. Licenses will be automatically activated
3. Install desired IDEs through the Toolbox interface

### Available IDEs
Through JetBrains Toolbox, you can install:
- IntelliJ IDEA (Community/Ultimate)
- PyCharm (Community/Professional)
- WebStorm
- PhpStorm
- CLion
- DataGrip
- GoLand
- RubyMine
- Rider
- And more...

## Troubleshooting

### Common Issues

**Desktop entry doesn't appear:**
```bash
update-desktop-database ~/.local/share/applications
```

**FUSE errors on older systems:**
- Ensure FUSE is properly configured
- Check if user is in `fuse` group (if required)

**Permission issues:**
- Verify target user exists
- Check file ownership in `~/.local/share/JetBrains/`

**Network issues:**
- Ensure internet connectivity
- Check firewall settings for HTTPS traffic

### Manual Verification
```bash
# Check if binary exists and is executable
ls -la ~/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox

# Test basic functionality
~/.local/share/JetBrains/Toolbox/bin/jetbrains-toolbox --version

# Check desktop integration
ls -la ~/.local/share/applications/jetbrains-toolbox.desktop
```

## Tags Available

Use these tags to run specific parts of the installation:

- `--tags jetbrains` - Complete JetBrains Toolbox installation
- `--tags dependencies` - Only install system dependencies
- `--tags download` - Only download and install Toolbox
- `--tags desktop` - Only setup desktop integration
- `--tags verification` - Only run verification checks

## Variables

### Customizable Variables
- `jetbrains_toolbox_user` - Target user for installation (default: `krizzo`)
- `hosts` - Target host group (default: `linux_clients`)

### Internal Variables
- `jetbrains_download_url` - Automatically fetched from JetBrains API
- `jetbrains_version` - Latest version from API
- `jetbrains_build` - Build number from API

## Security Considerations

- Downloads are performed over HTTPS
- GPG verification is not available for Toolbox (unlike Firefox)
- Binary is downloaded directly from JetBrains official API
- Installation is user-specific, not system-wide
- No elevated privileges required for normal operation

## References

- [JetBrains Toolbox Official Documentation](https://www.jetbrains.com/help/toolbox-app/)
- [Silent Installation Guide](https://www.jetbrains.com/help/toolbox-app/toolbox-app-silent-installation.html#toolbox_linux)
- [System Requirements](https://www.jetbrains.com/help/toolbox-app/toolbox-app-system-requirements.html)
- [JetBrains Toolbox Download Page](https://www.jetbrains.com/toolbox-app/)

## Support

For issues with:
- **Ansible playbooks**: Check this repository's issues
- **JetBrains Toolbox**: Visit [JetBrains Support](https://www.jetbrains.com/support/)
- **IDE-specific issues**: Check individual IDE documentation
