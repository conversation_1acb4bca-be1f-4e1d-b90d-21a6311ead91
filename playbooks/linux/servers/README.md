# Setup my linux machines

## Description

This dir is where all my linux host role playbooks should exist and how to structure them.

## Createing a new role

```bash
cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays/roles
ansible-galaxy role init [ROL<PERSON>_NAME]
```

## Requirements

```bash
ansible-galaxy collection install ansible.posix
ansible-galaxy collection install community.crypto
ansible-galaxy collection install community.docker
ansible-galaxy collection install community.network

```

python >= 3.8

`python -m pip install ansible`

## How to run it

Right now it only works for a debian system (possibly ubuntu haven't tested). This will take a machine that we can ssh to using our default user `krizzo` and knowing the root password set it up.

`ansible-playbook -v -K base.yaml`

## Manage SSH configuration

```bash
pip install stormssh paramiko termcolor Flask six pytest hypothesis
ansible-galaxy collection install community.general
```
