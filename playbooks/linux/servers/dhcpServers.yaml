---
# DHCP Server Configuration Playbook
#
# This playbook configures ISC KEA DHCP servers with high availability support.
# It follows Ansible best practices with proper variable organization and security.
#
# Ansible Vault file needs to be decrypted
#   ansible-vault encrypt dhcpServers_vault.yml
#
# Usage Examples:
#   # Standard deployment to all DHCP servers
#   ansible-playbook dhcpServers.yaml --ask-vault-pass
#
#   # Deploy to specific host group
#   ansible-playbook dhcpServers.yaml -e "target_hosts=dhcpTestServers" --ask-vault-pass
#
#   # Check mode (dry run)
#   ansible-playbook dhcpServers.yaml --check --ask-vault-pass
#
#   # Deploy only configuration files (skip package installation)
#   ansible-playbook dhcpServers.yaml --tags "config"
#
#   # Deploy with vault password file
#   ansible-playbook dhcpServers.yaml --vault-password-file ~/.ansible_vault_pass
#
# API Testing:
#   # Get active configuration via REST API
#   http --json POST http://*************:8000 command=config-get service:='["dhcp4"]'
#
# Requirements:
#   - Ansible 2.9+
#   - Target hosts must be in dhcpServers group in inventory
#   - Vault file must be decrypted or vault password provided
#   - User must have sudo privileges on target hosts

- name: Configure ISC KEA DHCP Servers
  hosts: "{{ target_hosts | default('dhcpServers') }}"
  gather_facts: true
  become: true
  serial: 1  # Deploy one server at a time to maintain service availability

  vars_files:
    - ./global_vars/users.yaml
    - ./group_vars/dhcpServers.yml
    - ./group_vars/dhcpServers_vault.yml

  vars:
    # Map vault variables to role variables
    kea_api_password: "{{ vault_kea_api_password }}"
    kea_api_username: "{{ vault_kea_api_username | default(kea_api_username) | default('keadm') }}"

  pre_tasks:
    - name: Check if vault variables are accessible
      ansible.builtin.debug:
        msg:
          - "Vault file status check:"
          - "  vault_kea_api_password defined: {{ vault_kea_api_password is defined }}"
          - "  vault_kea_api_username defined: {{ vault_kea_api_username is defined }}"
      tags: ['always', 'validation']

    - name: Validate vault variables are properly loaded
      ansible.builtin.assert:
        that:
          - vault_kea_api_password is defined
        fail_msg: |
          Vault variables are not accessible. This usually means:
          1. Vault file doesn't exist: group_vars/dhcpServers_vault.yml
          2. Vault file is not properly encrypted
          3. Wrong vault password provided
          4. Variable name mismatch in vault file

          Troubleshooting steps:
          1. Check if vault file exists: ls -la group_vars/dhcpServers_vault.yml
          2. Test vault decryption: ansible-vault view group_vars/dhcpServers_vault.yml
          3. Verify variable names in vault file match expected names
          4. Run test playbook: ansible-playbook test-vault-vars.yaml --ask-vault-pass
        success_msg: "Vault variables are accessible"
      tags: ['always', 'validation']

    - name: Validate vault password is not default
      ansible.builtin.assert:
        that:
          - vault_kea_api_password != "CHANGE_ME_BEFORE_ENCRYPTING"
          - vault_kea_api_password | length > 8
        fail_msg: |
          Vault password validation failed:
          - Password cannot be the default value
          - Password must be longer than 8 characters
          - Current password: {{ vault_kea_api_password }}
        success_msg: "Vault password meets security requirements"
      tags: ['always', 'validation']

    - name: Validate other required variables are defined
      ansible.builtin.assert:
        that:
          - primary_ip is defined
          - secondary_ip is defined
          - primary_hostname is defined
          - secondary_hostname is defined
          - network_gateway is defined
        fail_msg: |
          Network configuration variables are missing. Please ensure:
          1. Group variables are properly configured (group_vars/dhcpServers.yml)
          2. Host variables are defined for each DHCP server
        success_msg: "All network configuration variables are properly defined"
      tags: ['always', 'validation']

    - name: Debug variable mapping
      ansible.builtin.debug:
        msg:
          - "Vault variable status:"
          - "  vault_kea_api_password defined: {{ vault_kea_api_password is defined }}"
          - "  vault_kea_api_password length: {{ vault_kea_api_password | length if vault_kea_api_password is defined else 'N/A' }}"
          - "  kea_api_password mapped: {{ kea_api_password is defined }}"
          - "  kea_api_username: {{ kea_api_username }}"
      tags: ['debug', 'info']
      when: ansible_verbosity >= 1

    - name: Display deployment information
      ansible.builtin.debug:
        msg:
          - "Deploying DHCP configuration to: {{ inventory_hostname }}"
          - "Server role: {{ dhcp_server_role | default('undefined') }}"
          - "Target IP: {{ ansible_host }}"
          - "KEA API user: {{ kea_api_username }}"
      tags: ['always', 'info']

    - name: Check if this is a fresh installation
      ansible.builtin.stat:
        path: /etc/kea/kea-dhcp4.conf
      register: kea_config_exists
      tags: ['always', 'check']

    - name: Backup existing configuration
      ansible.builtin.copy:
        src: /etc/kea/
        dest: "/tmp/kea-backup-{{ ansible_date_time.epoch }}/"
        remote_src: true
        mode: '0600'
      when: kea_config_exists.stat.exists
      tags: ['backup', 'config']

  tasks:
    - name: Configure base system packages and users
      ansible.builtin.import_role:
        name: users
      tags: ['users', 'base']

    - name: Configure ISC KEA DHCP server
      ansible.builtin.import_role:
        name: dhcp-kea
      tags: ['dhcp', 'kea', 'config']

  post_tasks:
    - name: Verify DHCP services are running
      ansible.builtin.service:
        name: "{{ item }}"
        state: started
        enabled: true
      loop: "{{ kea_services }}"
      tags: ['verification', 'services']

    - name: Test KEA control agent API
      ansible.builtin.uri:
        url: "http://{{ ansible_default_ipv4.address }}:8000/"
        method: POST
        body_format: json
        body:
          command: "status-get"
        headers:
          Content-Type: "application/json"
        user: "{{ kea_api_username }}"
        password: "{{ kea_api_password }}"
        force_basic_auth: true
        status_code: 200
      register: kea_api_test
      retries: 3
      delay: 10
      tags: ['verification', 'api']

    - name: Display API test results
      ansible.builtin.debug:
        var: kea_api_test.json
      when: kea_api_test is succeeded
      tags: ['verification', 'api']

    - name: Create deployment summary
      ansible.builtin.debug:
        msg:
          - "=== DHCP Deployment Summary ==="
          - "Host: {{ inventory_hostname }} ({{ ansible_host }})"
          - "Role: {{ dhcp_server_role | default('undefined') }}"
          - "KEA Version: {{ ansible_facts.packages['kea'][0].version | default('unknown') }}"
          - "Services Status: {{ kea_services | join(', ') }}"
          - "API Endpoint: http://{{ ansible_default_ipv4.address }}:8000"
          - "Configuration: {{ 'Updated' if kea_config_exists.stat.exists else 'Fresh Install' }}"
          - "Deployment Time: {{ ansible_date_time.iso8601 }}"
      tags: ['always', 'summary']

  handlers:
    - name: Restart all KEA services
      ansible.builtin.service:
        name: "{{ item }}"
        state: restarted
      loop: "{{ kea_services }}"
      listen: "restart kea services"
