#!/bin/bash

# Validation script for init_combined.yaml playbook
# This script performs syntax checks and basic validation

set -e

echo "========================================="
echo "Validating init_combined.yaml playbook"
echo "========================================="

# Change to the correct directory
cd "$(dirname "$0")"

echo "Current directory: $(pwd)"

# Check if required files exist
echo "Checking required files..."

required_files=(
    "init_combined.yaml"
    "global_vars/users.yaml"
    "roles/base_packages/tasks/main.yml"
    "roles/users/tasks/main.yml"
    "roles/sudoers/tasks/main.yml"
    "ansible.cfg"
)

for file in "${required_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "✓ $file exists"
    else
        echo "✗ $file missing"
        exit 1
    fi
done

echo ""
echo "Checking YAML syntax..."

# Check main playbook syntax
echo "Validating init_combined.yaml..."
ansible-playbook --syntax-check init_combined.yaml
echo "✓ init_combined.yaml syntax is valid"

# Check users configuration
echo "Validating global_vars/users.yaml..."
python3 -c "
import yaml
import sys
try:
    with open('global_vars/users.yaml', 'r') as f:
        data = yaml.safe_load(f)
    if 'users_list' not in data:
        print('✗ users_list not found in users.yaml')
        sys.exit(1)
    if not isinstance(data['users_list'], list):
        print('✗ users_list is not a list')
        sys.exit(1)
    if len(data['users_list']) == 0:
        print('✗ users_list is empty')
        sys.exit(1)
    print('✓ users.yaml structure is valid')
    print(f'✓ Found {len(data[\"users_list\"])} users configured')
except Exception as e:
    print(f'✗ Error validating users.yaml: {e}')
    sys.exit(1)
"

echo ""
echo "Checking role dependencies..."

# Check if roles exist and have required structure
roles=("base_packages" "users" "sudoers")

for role in "${roles[@]}"; do
    role_path="roles/$role"
    if [[ -d "$role_path" ]]; then
        echo "✓ Role $role exists"
        
        # Check for required directories
        for dir in "tasks" "defaults"; do
            if [[ -d "$role_path/$dir" ]]; then
                echo "  ✓ $role/$dir directory exists"
            else
                echo "  ✗ $role/$dir directory missing"
            fi
        done
        
        # Check for main.yml files
        if [[ -f "$role_path/tasks/main.yml" ]]; then
            echo "  ✓ $role/tasks/main.yml exists"
            # Validate YAML syntax
            python3 -c "
import yaml
try:
    with open('$role_path/tasks/main.yml', 'r') as f:
        yaml.safe_load(f)
    print('  ✓ $role/tasks/main.yml syntax is valid')
except Exception as e:
    print(f'  ✗ $role/tasks/main.yml syntax error: {e}')
    exit(1)
"
        else
            echo "  ✗ $role/tasks/main.yml missing"
        fi
    else
        echo "✗ Role $role missing"
        exit 1
    fi
done

echo ""
echo "Checking inventory configuration..."

if [[ -f "hosts.yaml" ]]; then
    echo "✓ hosts.yaml inventory file exists"
    # Basic YAML syntax check
    python3 -c "
import yaml
try:
    with open('hosts.yaml', 'r') as f:
        data = yaml.safe_load(f)
    print('✓ hosts.yaml syntax is valid')
except Exception as e:
    print(f'✗ hosts.yaml syntax error: {e}')
    exit(1)
"
else
    echo "⚠ hosts.yaml not found - you'll need to specify inventory"
fi

echo ""
echo "Checking ansible configuration..."

if [[ -f "ansible.cfg" ]]; then
    echo "✓ ansible.cfg exists"
    # Check for required settings
    if grep -q "inventory" ansible.cfg; then
        echo "✓ inventory setting found in ansible.cfg"
    else
        echo "⚠ inventory setting not found in ansible.cfg"
    fi
else
    echo "⚠ ansible.cfg not found"
fi

echo ""
echo "========================================="
echo "Validation Summary"
echo "========================================="

echo "✓ All required files exist"
echo "✓ YAML syntax is valid"
echo "✓ Role structure is correct"
echo "✓ Playbook is ready to run"

echo ""
echo "To run the playbook:"
echo "  ansible-playbook -e \"hosts=testServers\" init_combined.yaml -k -K -v"
echo ""
echo "Make sure to:"
echo "1. Have target hosts defined in your inventory"
echo "2. Ensure SSH access to user 'aule' with password 'changeme'"
echo "3. Have root password ready for su privilege escalation"

echo ""
echo "Validation completed successfully!"
