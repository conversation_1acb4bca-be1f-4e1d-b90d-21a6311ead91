---
# Test playbook for optimized users and sudoers roles
- hosts: localhost
  connection: local
  gather_facts: true
  become: true
  
  vars:
    # Test user configurations
    users_list:
      - name: "testuser1"
        id: "3001"
        shell: "/bin/bash"
        ssh: true
        ssh_pub_keys:
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIAtrnC0RHMFtRV5x6hP1ttAyNLQdengHzk1hu0WZUl/U testuser1@test"
        sudoer: true
        sudo_permission: "admin"
        groups: ["users"]
        environment:
          EDITOR: "vim"
          LANG: "en_US.UTF-8"
        
      - name: "testuser2"
        id: "3002"
        shell: "/usr/bin/zsh"
        ssh: true
        ssh_pub_keys:
          - "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQC... testuser2@test"
        sudoer: true
        sudo_permission: "developer"
        requiretty: true
        groups: ["developers"]
        
      - name: "testuser3"
        id: "3003"
        shell: "/bin/bash"
        ssh: false
        sudoer: true
        sudo_permission: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl restart nginx"
        groups: ["operators"]
        
      - name: "testuser4"
        id: "3004"
        shell: "/bin/bash"
        ssh: true
        ssh_pub_keys:
          - "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIBtrnC0RHMFtRV5x6hP1ttAyNLQdengHzk1hu0WZUl/V testuser4@test"
        sudoer: false  # No sudo access
        groups: ["users"]

  pre_tasks:
    - name: Display test information
      ansible.builtin.debug:
        msg:
          - "=== Users and Sudoers Role Test ==="
          - "Testing {{ users_list | length }} users"
          - "Sudoers enabled: {{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list | length }}"
          - "SSH enabled: {{ users_list | selectattr('ssh', 'defined') | selectattr('ssh', 'equalto', true) | list | length }}"

    - name: Ensure test groups exist
      ansible.builtin.group:
        name: "{{ group_name }}"
        state: present
      loop:
        - "users"
        - "developers"
        - "operators"
      loop_control:
        loop_var: group_name

  tasks:
    - name: Test users role
      ansible.builtin.include_role:
        name: users
      tags: ['users']

    - name: Test sudoers role
      ansible.builtin.include_role:
        name: sudoers
      tags: ['sudoers']

  post_tasks:
    - name: Verify users were created
      ansible.builtin.getent:
        database: passwd
        key: "{{ user_item.name }}"
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"
      register: user_verification

    - name: Verify sudoers files were created
      ansible.builtin.stat:
        path: "/etc/sudoers.d/ansible-managed-{{ user_item.name }}"
      loop: "{{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"
      register: sudoers_verification

    - name: Verify SSH keys were deployed
      ansible.builtin.stat:
        path: "/home/<USER>/.ssh/authorized_keys"
      loop: "{{ users_list | selectattr('ssh', 'defined') | selectattr('ssh', 'equalto', true) | list }}"
      loop_control:
        loop_var: user_item
        label: "{{ user_item.name }}"
      register: ssh_verification

    - name: Display test results
      ansible.builtin.debug:
        msg:
          - "=== Test Results ==="
          - "Users created: {{ user_verification.results | selectattr('ansible_facts', 'defined') | list | length }}/{{ users_list | length }}"
          - "Sudoers files created: {{ sudoers_verification.results | selectattr('stat.exists', 'equalto', true) | list | length }}/{{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list | length }}"
          - "SSH keys deployed: {{ ssh_verification.results | selectattr('stat.exists', 'equalto', true) | list | length }}/{{ users_list | selectattr('ssh', 'defined') | selectattr('ssh', 'equalto', true) | list | length }}"

    - name: Validate sudoers syntax
      ansible.builtin.command:
        cmd: "/usr/sbin/visudo -c"
      register: sudoers_syntax_check
      failed_when: sudoers_syntax_check.rc != 0
      changed_when: false

    - name: Display final validation
      ansible.builtin.debug:
        msg:
          - "✅ All tests completed successfully!"
          - "✅ Users created and configured"
          - "✅ Sudoers files deployed and validated"
          - "✅ SSH access configured"
          - "✅ Shell environments set up"

  handlers:
    - name: Cleanup test users
      ansible.builtin.user:
        name: "{{ user_item.name }}"
        state: absent
        remove: true
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
      listen: "cleanup test"

    - name: Cleanup test sudoers files
      ansible.builtin.file:
        path: "/etc/sudoers.d/ansible-managed-{{ user_item.name }}"
        state: absent
      loop: "{{ users_list }}"
      loop_control:
        loop_var: user_item
      listen: "cleanup test"
