#!/usr/bin/env python3
"""
YAML Syntax Validation Script for Users Configuration
Validates the restructured users.yaml file for syntax and basic structure
"""

import yaml
import sys
import os
from pathlib import Path

def validate_yaml_syntax(file_path):
    """Validate YAML syntax"""
    try:
        with open(file_path, 'r') as file:
            data = yaml.safe_load(file)
        print(f"✅ YAML syntax is valid: {file_path}")
        return data
    except yaml.YAMLError as e:
        print(f"❌ YAML syntax error in {file_path}: {e}")
        return None
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return None

def validate_users_structure(data):
    """Validate users list structure"""
    if 'users_list' not in data:
        print("❌ Missing 'users_list' key")
        return False
    
    users_list = data['users_list']
    if not isinstance(users_list, list):
        print("❌ 'users_list' must be a list")
        return False
    
    print(f"✅ Found {len(users_list)} users in configuration")
    
    # Validate each user
    required_fields = ['name', 'id', 'shell']
    usernames = []
    uids = []
    
    for i, user in enumerate(users_list):
        if not isinstance(user, dict):
            print(f"❌ User {i} is not a dictionary")
            return False
        
        # Check required fields
        for field in required_fields:
            if field not in user:
                print(f"❌ User {i} missing required field: {field}")
                return False
        
        username = user['name']
        uid = user['id']
        
        # Check for duplicates
        if username in usernames:
            print(f"❌ Duplicate username: {username}")
            return False
        if uid in uids:
            print(f"❌ Duplicate UID: {uid}")
            return False
        
        usernames.append(username)
        uids.append(uid)
        
        # Validate SSH configuration
        if user.get('ssh', False):
            if 'ssh_pub_keys' not in user:
                print(f"❌ User {username} has ssh=true but no ssh_pub_keys")
                return False
            
            ssh_keys = user['ssh_pub_keys']
            if not isinstance(ssh_keys, list) or len(ssh_keys) == 0:
                print(f"❌ User {username} ssh_pub_keys must be a non-empty list")
                return False
            
            # Validate SSH key format
            for key in ssh_keys:
                if not key.startswith(('ssh-rsa', 'ssh-ed25519', 'ssh-dss', 'ecdsa-sha2')):
                    print(f"❌ User {username} has invalid SSH key format")
                    return False
        
        # Validate sudo configuration
        if user.get('sudoer', False):
            if 'sudo_permission' not in user:
                print(f"❌ User {username} has sudoer=true but no sudo_permission")
                return False
        
        # Validate shell path
        shell = user['shell']
        if not shell.startswith('/'):
            print(f"❌ User {username} shell path must be absolute: {shell}")
            return False
        
        # Validate UID range
        try:
            uid_int = int(uid)
            if uid_int < 1000 or uid_int > 65534:
                print(f"❌ User {username} UID {uid} outside valid range (1000-65534)")
                return False
        except ValueError:
            print(f"❌ User {username} UID must be numeric: {uid}")
            return False
        
        print(f"✅ User {username} (UID: {uid}) configuration is valid")
    
    return True

def validate_global_settings(data):
    """Validate global settings structure"""
    expected_sections = [
        'users_global_settings',
        'users_sudo_templates', 
        'users_required_groups'
    ]
    
    for section in expected_sections:
        if section in data:
            print(f"✅ Found global section: {section}")
        else:
            print(f"⚠️  Optional global section missing: {section}")
    
    return True

def main():
    """Main validation function"""
    print("=== Users Configuration YAML Validation ===")
    
    # Get the script directory
    script_dir = Path(__file__).parent
    users_file = script_dir / "global_vars" / "users.yaml"
    
    print(f"Validating: {users_file}")
    
    # Validate YAML syntax
    data = validate_yaml_syntax(users_file)
    if data is None:
        sys.exit(1)
    
    # Validate users structure
    if not validate_users_structure(data):
        sys.exit(1)
    
    # Validate global settings
    if not validate_global_settings(data):
        sys.exit(1)
    
    print("\n✅ All validations passed!")
    print("✅ Configuration is ready for deployment")
    
    # Display summary
    users_count = len(data['users_list'])
    ssh_users = len([u for u in data['users_list'] if u.get('ssh', False)])
    sudo_users = len([u for u in data['users_list'] if u.get('sudoer', False)])
    
    print(f"\n📊 Configuration Summary:")
    print(f"   Total users: {users_count}")
    print(f"   SSH enabled: {ssh_users}")
    print(f"   Sudo enabled: {sudo_users}")
    print(f"   Standard users: {users_count - sudo_users}")

if __name__ == "__main__":
    main()
