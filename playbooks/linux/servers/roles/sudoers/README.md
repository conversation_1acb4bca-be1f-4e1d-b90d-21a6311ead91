Sudoers Management Role
======================

An advanced Ansible role for managing sudo permissions with comprehensive validation, templating, and cleanup capabilities.

Requirements
------------

- Ansible 2.9 or higher
- Target systems: Linux with sudo installed
- Root/sudo privileges for sudoers file management
- `visudo` command available for validation

Features
--------

- **Syntax Validation**: All sudoers files validated with visudo
- **Template Support**: Rich templating with security settings
- **Common Rules**: Pre-defined sudo permission templates
- **Group Support**: Manage group-based sudo permissions
- **Cleanup**: Automatic removal of orphaned sudoers files
- **Backup**: Automatic backup of existing configurations
- **Security**: Enhanced logging and security settings

Role Variables
--------------

### Default Variables (defaults/main.yml)

```yaml
# Sudoers configuration
sudoers_path: "/etc/sudoers.d"
sudoers_file_mode: "0440"
sudoers_validate_syntax: true
sudoers_backup_files: true

# Default permissions
sudoers_default_permission: "ALL=(ALL) NOPASSWD: ALL"
sudoers_default_requiretty: false

# Common sudo rules
sudoers_common_rules:
  admin: "ALL=(ALL) NOPASSWD: ALL"
  developer: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl, /usr/bin/docker, /usr/bin/git"
  operator: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl restart *, /usr/bin/systemctl status *"
  readonly: "ALL=(ALL) NOPASSWD: /usr/bin/cat /var/log/*, /usr/bin/tail /var/log/*"

# Group-based rules
sudoers_groups:
  - name: "sudo"
    rule: "%sudo ALL=(ALL:ALL) ALL"
  - name: "wheel"
    rule: "%wheel ALL=(ALL) ALL"
```

### User Configuration Format

Users must have `sudoer: true` and can specify:

```yaml
users_list:
  - name: "username"
    sudoer: true                    # Required: Enable sudo access
    sudo_permission: "admin"        # Optional: Use common rule
    # OR
    sudo_permission: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl"  # Custom rule
    requiretty: false              # Optional: Require TTY
```

### Common Sudo Rules

Pre-defined rules for common use cases:

- **admin**: Full sudo access without password
- **developer**: System services, Docker, Git access
- **operator**: Service management only
- **readonly**: Log file access only

Dependencies
------------

- Must be used with the `users` role or compatible user management
- Requires `users_list` variable with sudoer configurations

Example Playbook
----------------

```yaml
- hosts: servers
  become: true
  vars:
    users_list:
      - name: "krizzo"
        id: "1985"
        sudoer: true
        sudo_permission: "admin"
        requiretty: false
      - name: "developer"
        id: "2000"
        sudoer: true
        sudo_permission: "developer"
      - name: "operator"
        id: "2001"
        sudoer: true
        sudo_permission: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl restart nginx"
        requiretty: true
  roles:
    - users
    - sudoers
```

### Advanced Configuration

```yaml
# Custom common rules
sudoers_common_rules:
  webadmin: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl * nginx, /usr/bin/systemctl * apache2"
  dbadmin: "ALL=(ALL) NOPASSWD: /usr/bin/systemctl * mysql, /usr/bin/systemctl * postgresql"

# Enhanced security
sudoers_default_requiretty: true
sudoers_validate_all_files: true
sudoers_remove_orphaned_files: true

# Custom groups
sudoers_groups:
  - name: "webadmins"
    rule: "%webadmins ALL=(ALL) NOPASSWD: /usr/bin/systemctl * nginx"
  - name: "dbadmins"
    rule: "%dbadmins ALL=(ALL) NOPASSWD: /usr/bin/systemctl * mysql"
```

Security Features
-----------------

- **Syntax Validation**: Every sudoers file validated before deployment
- **Secure Permissions**: Files created with 0440 permissions
- **Enhanced Logging**: Input/output logging for sudo commands
- **Secure PATH**: Restricted PATH environment for sudo commands
- **TTY Control**: Configurable TTY requirements per user
- **Backup & Recovery**: Automatic backup of existing configurations

Tags
----

- `sudoers`: All sudoers management tasks
- `validation`: Sudoers file validation
- `users`: User-specific sudoers configuration
- `groups`: Group-based sudoers configuration
- `cleanup`: Orphaned file cleanup

Troubleshooting
---------------

### Common Issues

1. **Syntax Errors**: All files are validated with visudo before deployment
2. **Permission Denied**: Ensure role runs with sudo/root privileges
3. **Orphaned Files**: Use `sudoers_remove_orphaned_files: true` for cleanup

### Validation

```bash
# Manual validation
sudo visudo -c -f /etc/sudoers.d/ansible-managed-username

# Check all sudoers files
sudo visudo -c
```

License
-------

MIT

Author Information
------------------

Maintained by krizzo for home lab infrastructure management.
Optimized for security and following sudo best practices.
