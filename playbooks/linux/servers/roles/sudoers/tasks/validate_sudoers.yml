---
# Validate all sudoers files

- name: Get all sudoers files for validation
  ansible.builtin.find:
    paths: "{{ sudoers_path }}"
    patterns: "*"
    file_type: file
  register: all_sudoers_files

- name: Validate individual sudoers files
  ansible.builtin.command:
    cmd: "/usr/sbin/visudo -c -f {{ sudoers_file.path }}"
  loop: "{{ all_sudoers_files.files }}"
  loop_control:
    loop_var: sudoers_file
    label: "{{ sudoers_file.path | basename }}"
  register: validation_results
  failed_when: validation_results.rc != 0
  changed_when: false

- name: Validate main sudoers file
  ansible.builtin.command:
    cmd: "/usr/sbin/visudo -c"
  register: main_sudoers_validation
  failed_when: main_sudoers_validation.rc != 0
  changed_when: false

- name: Display validation summary
  ansible.builtin.debug:
    msg:
      - "Sudoers validation complete"
      - "Files validated: {{ all_sudoers_files.files | length }}"
      - "Main sudoers file: {{ 'VALID' if main_sudoers_validation.rc == 0 else 'INVALID' }}"
      - "All files valid: {{ validation_results.results | selectattr('rc', 'ne', 0) | list | length == 0 }}"
  when: ansible_verbosity >= 1

- name: Report validation failures
  ansible.builtin.fail:
    msg: |
      Sudoers validation failed for the following files:
      {% for result in validation_results.results %}
      {% if result.rc != 0 %}
      - {{ result.sudoers_file.path }}: {{ result.stderr }}
      {% endif %}
      {% endfor %}
  when: validation_results.results | selectattr('rc', 'ne', 0) | list | length > 0
