---
# Configure sudoers for groups

- name: Configure group-based sudo rules
  ansible.builtin.lineinfile:
    path: "{{ sudoers_path }}/{{ sudoers_managed_prefix }}groups"
    line: "{{ group_config.rule }}"
    regexp: "^%{{ group_config.name }}\\s+"
    create: true
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
    state: present
    validate: "/usr/sbin/visudo -c -f %s"
    backup: "{{ sudoers_backup_files }}"
  loop: "{{ sudoers_groups }}"
  loop_control:
    loop_var: group_config
    label: "{{ group_config.name }}"

- name: Add header to groups sudoers file
  ansible.builtin.lineinfile:
    path: "{{ sudoers_path }}/{{ sudoers_managed_prefix }}groups"
    line: "# {{ ansible_managed }} - Group-based sudo rules"
    insertbefore: BOF
    create: true
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
  when: sudoers_template_header | bool

- name: Mark groups file as managed
  ansible.builtin.set_fact:
    managed_sudoers_files: "{{ managed_sudoers_files | default([]) + [sudoers_path + '/' + sudoers_managed_prefix + 'groups'] }}"
