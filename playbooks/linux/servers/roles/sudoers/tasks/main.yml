---
# tasks file for sudoers

- name: Validate sudoers configuration
  ansible.builtin.assert:
    that:
      - users_list is defined
      - users_list is iterable
    fail_msg: "users_list must be defined and iterable"
    success_msg: "Sudoers configuration validation passed"
  tags: ['sudoers', 'validation']

- name: Ensure sudoers.d directory exists
  ansible.builtin.file:
    path: "{{ sudoers_path }}"
    state: directory
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: '0755'
  tags: ['sudoers', 'setup']

- name: Get existing sudoers files
  ansible.builtin.find:
    paths: "{{ sudoers_path }}"
    patterns: "{{ sudoers_managed_prefix }}*"
  register: existing_sudoers_files
  when: sudoers_remove_orphaned_files | bool
  tags: ['sudoers', 'cleanup']

- name: Configure group-based sudo rules
  ansible.builtin.include_tasks: configure_group_sudoers.yml
  when: sudoers_groups is defined and sudoers_groups | length > 0
  tags: ['sudoers', 'groups']

- name: Process user sudoers configurations
  ansible.builtin.include_tasks: configure_user_sudoers.yml
  loop: "{{ users_list | selectattr('sudoer', 'defined') | selectattr('sudoer', 'equalto', true) | list }}"
  loop_control:
    loop_var: user_config
    label: "{{ user_config.name }}"
  tags: ['sudoers', 'users']

- name: Remove orphaned sudoers files
  ansible.builtin.include_tasks: cleanup_sudoers.yml
  when: sudoers_remove_orphaned_files | bool
  tags: ['sudoers', 'cleanup']

- name: Validate all sudoers files
  ansible.builtin.include_tasks: validate_sudoers.yml
  when: sudoers_validate_all_files | bool
  tags: ['sudoers', 'validation']
