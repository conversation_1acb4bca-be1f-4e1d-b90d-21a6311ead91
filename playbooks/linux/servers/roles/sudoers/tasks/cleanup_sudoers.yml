---
# Cleanup orphaned sudoers files

- name: Get list of managed files
  ansible.builtin.set_fact:
    current_managed_files: "{{ managed_sudoers_files | default([]) | map('basename') | list }}"

- name: Get list of existing managed files
  ansible.builtin.set_fact:
    existing_managed_files: "{{ existing_sudoers_files.files | map(attribute='path') | map('basename') | list }}"

- name: Identify orphaned sudoers files
  ansible.builtin.set_fact:
    orphaned_files: "{{ existing_managed_files | difference(current_managed_files) }}"

- name: Display orphaned files
  ansible.builtin.debug:
    msg:
      - "Current managed files: {{ current_managed_files }}"
      - "Existing managed files: {{ existing_managed_files }}"
      - "Orphaned files to remove: {{ orphaned_files }}"
  when: ansible_verbosity >= 1

- name: Backup orphaned sudoers files
  ansible.builtin.copy:
    src: "{{ sudoers_path }}/{{ orphaned_file }}"
    dest: "{{ sudoers_path }}/{{ orphaned_file }}{{ sudoers_backup_suffix }}"
    remote_src: true
    owner: "{{ sudoers_file_owner }}"
    group: "{{ sudoers_file_group }}"
    mode: "{{ sudoers_file_mode }}"
  loop: "{{ orphaned_files }}"
  loop_control:
    loop_var: orphaned_file
  when: 
    - orphaned_files | length > 0
    - sudoers_backup_files | bool

- name: Remove orphaned sudoers files
  ansible.builtin.file:
    path: "{{ sudoers_path }}/{{ orphaned_file }}"
    state: absent
  loop: "{{ orphaned_files }}"
  loop_control:
    loop_var: orphaned_file
  when: orphaned_files | length > 0
  register: cleanup_result

- name: Display cleanup summary
  ansible.builtin.debug:
    msg:
      - "Orphaned files removed: {{ orphaned_files | length }}"
      - "Files: {{ orphaned_files }}"
  when: 
    - cleanup_result is defined
    - cleanup_result.changed | default(false)
    - orphaned_files | length > 0
