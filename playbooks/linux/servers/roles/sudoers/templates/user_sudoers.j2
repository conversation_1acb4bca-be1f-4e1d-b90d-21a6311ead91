# {{ ansible_managed }}
# Sudoers configuration for user: {{ username }}
# Generated on: {{ ansible_date_time.iso8601 }}

{% if requiretty %}
# Require TTY for this user
Defaults:{{ username }} requiretty
{% else %}
# Allow sudo without TTY for this user
Defaults:{{ username }} !requiretty
{% endif %}

# Sudo permissions for {{ username }}
{{ username }} {{ sudo_rule }}

# Additional security settings
Defaults:{{ username }} env_reset
Defaults:{{ username }} mail_badpass
Defaults:{{ username }} secure_path="/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

# Logging
Defaults:{{ username }} log_input,log_output
Defaults:{{ username }} logfile="/var/log/sudo/{{ username }}.log"
