---
# tasks file for retropie

- name: Get available groups
  ansible.builtin.getent:
    database: group

- name: Get available Users
  ansible.builtin.getent:
    database: passwd

- name: "Create {{ user_name }} user and group"
  block:
    - name: "Create group {{ user_name }} with {{ user_id }}"
      ansible.builtin.group:
        name: "{{ user_name }}"
        gid: "{{ user_id }}"
      when:
        - "user_name not in ansible_facts.getent_group"
        - "user_id not in item.value"
      loop: "{{ ansible_facts.getent_group | dict2items }}"

    - name: "Create user {{ user_name }} with {{ user_id }}"
      ansible.builtin.user:
        name: "{{ user_name }}"
        group: "{{ user_name }}" # Primary Group
        shell: "{{ user_shell }}"
        uid: "{{ user_id }}"
        create_home: yes
      when:
        - "user_name not in ansible_facts.getent_passwd" # Only create the user if they don't exist
        - "user_id not in item.value[1]"
      loop: "{{ ansible_facts.getent_passwd | dict2items }}"

  # rescue: # if the ID already exists lets create a random number
  #   - name: Create random number between 2000-2500 for group id
  #     ansible.builtin.set_fact:
  #       random_num: "{{ range(2000, 2500) | random(seed=item) }}"
  #     run_once: true
  #     with_items:
  #       - string

  #   - name: "Create group {{ user_name }} with gid {{ random_num }} when not available"
  #     ansible.builtin.group:
  #       name: "{{ user_name }}"
  #       gid: "{{ random_num }}"
  #     when:
  #       - "user_name not in ansible_facts.getent_group"
  #       - "random_num not in item.value"
  #     loop: "{{ ansible_facts.getent_group | dict2items }}"

- name: "Create {{ sudoers_path }}/{{ user_name }} file"
  become: true
  ansible.builtin.lineinfile:
    path: "{{ sudoers_path }}/{{ user_name }}"
    line: "{{ user_name }} {{ user_sudo_permission }}"
    create: yes
    owner: root
    group: root
    mode: "0440"
    state: present
    validate: "/usr/sbin/visudo -c -f %s"
