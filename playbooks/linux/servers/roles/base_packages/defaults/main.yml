---
# defaults file for base_packages

# Base packages for RHEL-based systems (Rocky, CentOS, RHEL, Fedora)
rhel_base_packages:
  - sudo
  - epel-release
  - elrepo-release
  - kmod-wireguard
  - wireguard-tools
  - neovim
  - mtr
  - curl
  - gpg
  - zsh
  - tmux
  - jq
  - git
  - tcpdump
  - nfs-utils
  - dnf-utils
  - bind-utils
  - python39
  - python3-libselinux
  - NetworkManager
  - podman
  - rsync
  - ncdu
  - btop

# Python packages for RHEL systems
rhel_pip_packages:
  - selinux

# Base packages for Debian-based systems (Debian, Ubuntu)
debian_base_packages:
  - apt-file
  - bat
  - bind9utils
  - curl
  - gpg
  - dnsutils
  - git
  - iptables
  - jq
  - mtr
  - ncdu
  - neovim
  - network-manager
  - nfs-common
  - rsync
  - sudo
  - tcpdump
  - tmux
  - zsh
  - smbclient
  - cifs-utils
  - btop
  - prettyping

# Python packages for Debian systems (currently none defined)
debian_pip_packages: []

# Third-party repositories configuration
third_party_repos:
  eza:
    enabled: true
    key_url: "https://raw.githubusercontent.com/eza-community/eza/main/deb.asc"
    key_path: "/etc/apt/keyrings/gierens.gpg"
    repo_line: "deb [signed-by=/etc/apt/keyrings/gierens.gpg] http://deb.gierens.de stable main"
    repo_file: "/etc/apt/sources.list.d/gierens.list"
    packages:
      - eza

  gping:
    enabled: true
    key_url: "https://azlux.fr/repo.gpg.key"
    key_path: "/usr/share/keyrings/azlux.gpg"
    repo_line: "deb [signed-by=/usr/share/keyrings/azlux.gpg] https://packages.azlux.fr/debian/ bookworm main"
    repo_file: "/etc/apt/sources.list.d/azlux.list"
    packages:
      - gping

# External tools configuration
external_tools:
  uv:
    enabled: true
    install_script: "https://astral.sh/uv/install.sh"
    check_command: "uv --version"
    post_install_packages:
      - name: "isd-tui"
        python_version: "3.12"

# Editor configuration
default_editor:
  enabled: true
  editor_path: "/usr/bin/nvim"
  alternatives_name: "editor"

# Virtualization-specific packages
virtualization_packages:
  xen:
    packages:
      - xe-guest-utilities-latest
    services:
      - xe-linux-distribution

# Package manager settings
package_manager_settings:
  update_cache: true
  state: "present"  # Use 'present' instead of 'latest' for stability
  cache_valid_time: 3600  # For apt, cache valid time in seconds
