---

# Ensure sudo is installed first for fresh systems
- name: Install sudo package first (critical for init)
  ansible.builtin.dnf:
    name: sudo
    state: "{{ package_manager_settings.state }}"
    update_cache: "{{ package_manager_settings.update_cache }}"
  tags: ['sudo', 'critical']

- name: Update DNF cache
  ansible.builtin.dnf:
    update_cache: true
  tags: ['cache']

- name: Install base RHEL packages
  ansible.builtin.dnf:
    name: "{{ rhel_base_packages }}"
    state: "{{ package_manager_settings.state }}"
    update_cache: false  # Already updated above
  notify: "dnf autoremove"
  tags: ['base_packages']

- name: Install Python packages for RHEL systems
  ansible.builtin.pip:
    name: "{{ rhel_pip_packages }}"
    state: "{{ package_manager_settings.state }}"
    executable: pip3
  when: rhel_pip_packages | length > 0
  become: false
  tags: ['python_packages']

# Virtualization-specific packages
- name: Install virtualization-specific packages
  ansible.builtin.include_tasks: install_virtualization_packages.yaml
  when:
    - ansible_facts['virtualization_type'] is defined
    - ansible_facts['virtualization_type'] | lower in virtualization_packages
  tags: ['virtualization']

# Editor configuration
- name: Check if neovim is installed
  ansible.builtin.command: "{{ default_editor.editor_path }} --version"
  register: nvim_check
  failed_when: false
  changed_when: false
  when: default_editor.enabled | default(false)
  tags: ['editor']

- name: Set neovim as default editor
  community.general.alternatives:
    name: "{{ default_editor.alternatives_name }}"
    path: "{{ default_editor.editor_path }}"
  when:
    - default_editor.enabled | default(false)
    - nvim_check.rc == 0
  tags: ['editor']
