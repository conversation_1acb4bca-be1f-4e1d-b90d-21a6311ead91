---
# tasks file for base_packages

- name: Display system information
  ansible.builtin.debug:
    msg: |
      System Information:
      - OS Family: {{ ansible_facts['os_family'] }}
      - Distribution: {{ ansible_facts['distribution'] }}
      - Distribution Version: {{ ansible_facts['distribution_version'] }}
      - Virtualization Role: {{ ansible_facts['virtualization_role'] | default('N/A') }}
      - Virtualization Type: {{ ansible_facts['virtualization_type'] | default('N/A') }}
    verbosity: 1
  tags: ['info']

- name: Set OS family facts
  ansible.builtin.set_fact:
    is_debian_family: "{{ ansible_facts['os_family'] | lower == 'debian' }}"
    is_rhel_family: "{{ ansible_facts['os_family'] | lower in ['redhat', 'rocky', 'centos', 'fedora'] }}"
  tags: ['always']

- name: Validate supported OS family
  ansible.builtin.fail:
    msg: "Unsupported OS family: {{ ansible_facts['os_family'] }}. This role supports Debian and RHEL-based systems only."
  when: not (is_debian_family or is_rhel_family)
  tags: ['validation']

- name: Install packages on Debian-based systems
  ansible.builtin.include_tasks: debian.yaml
  when: is_debian_family
  tags: ['packages', 'debian']

- name: Install packages on RHEL-based systems
  ansible.builtin.include_tasks: rhel.yaml
  when: is_rhel_family
  tags: ['packages', 'rhel']