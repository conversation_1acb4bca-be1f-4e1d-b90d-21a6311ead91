---
# Task file for installing external tools

- name: "Install {{ tool_item.key }} via install script"
  ansible.builtin.shell: |
    curl -fsSL {{ tool_item.value.install_script }} | sh
  args:
    creates: "{{ ansible_env.HOME }}/.local/bin/{{ tool_item.key }}"
  become: false
  environment:
    PATH: "{{ ansible_env.PATH }}:{{ ansible_env.HOME }}/.local/bin"
  register: tool_install
  retries: 3
  delay: 5
  until: tool_install is succeeded

- name: "Verify {{ tool_item.key }} installation"
  ansible.builtin.command: "{{ tool_item.value.check_command }}"
  environment:
    PATH: "{{ ansible_env.PATH }}:{{ ansible_env.HOME }}/.local/bin"
  changed_when: false
  become: false

- name: "Install post-install packages for {{ tool_item.key }}"
  ansible.builtin.shell: |
    {{ tool_item.key }} tool install --python={{ item.python_version | default('3.11') }} {{ item.name }}
  environment:
    PATH: "{{ ansible_env.PATH }}:{{ ansible_env.HOME }}/.local/bin"
  loop: "{{ tool_item.value.post_install_packages | default([]) }}"
  become: false
  register: post_install
  failed_when: 
    - post_install.rc != 0
    - "'already installed' not in post_install.stderr"
  changed_when: "'already installed' not in post_install.stderr"
