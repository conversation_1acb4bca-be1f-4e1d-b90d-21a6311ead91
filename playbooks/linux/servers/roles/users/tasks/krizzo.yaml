export ssh_key_algorithm="ed25519"
export system_usage_name="personal"
ssh-keygen -o -a 300 -t "${ssh_key_algorithm}" -f ~/.ssh/id_${system_usage_name}_"${ssh_key_algorithm}" -C "$(whoami)@$(hostname)-${system_usage_name}"
cat ~/.ssh/id_${system_usage_name}_"${ssh_key_algorithm}".pub >> ~/.ssh/

curl -sSfL https://raw.githubusercontent.com/ajeetdsouza/zoxide/main/install.sh | sh
curl -sL --proto-redir -all,https https://raw.githubusercontent.com/zplug/installer/master/installer.zsh | zsh
git clone https://github.com/tmux-plugins/tpm ~/.tmux/plugins/tpm
curl --proto '=https' --tlsv1.2 -LsSf https://setup.atuin.sh | sh