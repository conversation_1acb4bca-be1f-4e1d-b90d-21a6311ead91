---
# Optimized user processing task file
# This file processes a single user efficiently

- name: Set user facts for {{ user_config.name }}
  ansible.builtin.set_fact:
    current_user:
      name: "{{ user_config.name }}"
      uid: "{{ user_config.id | int }}"
      gid: "{{ user_config.id | int }}"
      shell: "{{ user_config.shell | default(users_default_shell) }}"
      groups: "{{ user_config.groups | default(users_default_groups) }}"
      create_home: "{{ user_config.create_home | default(users_default_create_home) }}"
      ssh_enabled: "{{ user_config.ssh | default(false) }}"
      ssh_keys: "{{ user_config.ssh_pub_keys | default([]) }}"
      home_dir: "/home/<USER>"
    user_exists: "{{ user_config.name in getent_passwd | default({}) }}"
    group_exists: "{{ user_config.name in getent_group | default({}) }}"
    uid_in_use: "{{ getent_passwd | default({}) | dict2items | map(attribute='value.1') | map('int') | select('equalto', user_config.id | int) | list | length > 0 }}"
    gid_in_use: "{{ getent_group | default({}) | dict2items | map(attribute='value.1') | map('int') | select('equalto', user_config.id | int) | list | length > 0 }}"

- name: Validate UID/GID availability for {{ current_user.name }}
  ansible.builtin.assert:
    that:
      - not uid_in_use or user_exists
      - not gid_in_use or group_exists
      - current_user.uid >= users_system_uid_min
      - current_user.uid <= users_system_uid_max
    fail_msg: |
      User {{ current_user.name }} configuration conflicts:
      - UID {{ current_user.uid }} in use: {{ uid_in_use }}
      - GID {{ current_user.gid }} in use: {{ gid_in_use }}
      - UID range valid: {{ current_user.uid >= users_system_uid_min and current_user.uid <= users_system_uid_max }}
    success_msg: "UID/GID validation passed for {{ current_user.name }}"
  when: users_validate_uid_gid | bool

- name: Create primary group for {{ current_user.name }}
  ansible.builtin.group:
    name: "{{ current_user.name }}"
    gid: "{{ current_user.gid }}"
    state: present
  when: not group_exists
  register: group_creation

- name: Create user {{ current_user.name }}
  ansible.builtin.user:
    name: "{{ current_user.name }}"
    uid: "{{ current_user.uid }}"
    group: "{{ current_user.name }}"
    groups: "{{ current_user.groups | join(',') if current_user.groups else omit }}"
    shell: "{{ current_user.shell }}"
    home: "{{ current_user.home_dir }}"
    create_home: "{{ current_user.create_home }}"
    state: present
    append: true
  register: user_creation

- name: Update user shell for {{ current_user.name }}
  ansible.builtin.user:
    name: "{{ current_user.name }}"
    shell: "{{ current_user.shell }}"
  when: 
    - user_exists
    - ansible_facts.getent_passwd[current_user.name][5] != current_user.shell
  register: shell_update

- name: Configure SSH access for {{ current_user.name }}
  block:
    - name: Ensure SSH directory exists for {{ current_user.name }}
      ansible.builtin.file:
        path: "{{ current_user.home_dir }}/.ssh"
        state: directory
        owner: "{{ current_user.name }}"
        group: "{{ current_user.name }}"
        mode: "{{ users_ssh_dir_mode }}"

    - name: Set SSH authorized keys for {{ current_user.name }}
      ansible.posix.authorized_key:
        user: "{{ current_user.name }}"
        key: "{{ ssh_key }}"
        state: present
        manage_dir: false
      loop: "{{ current_user.ssh_keys }}"
      loop_control:
        loop_var: ssh_key
        label: "{{ ssh_key.split()[-1] if ssh_key.split() | length > 2 else 'key' }}"
      when: current_user.ssh_keys | length > 0

    - name: Set authorized_keys file permissions for {{ current_user.name }}
      ansible.builtin.file:
        path: "{{ current_user.home_dir }}/.ssh/{{ users_ssh_authorized_keys_file }}"
        owner: "{{ current_user.name }}"
        group: "{{ current_user.name }}"
        mode: "{{ users_ssh_authorized_keys_mode }}"
      when: current_user.ssh_keys | length > 0

  when: current_user.ssh_enabled | bool

- name: Display user creation summary for {{ current_user.name }}
  ansible.builtin.debug:
    msg:
      - "User: {{ current_user.name }}"
      - "UID/GID: {{ current_user.uid }}/{{ current_user.gid }}"
      - "Shell: {{ current_user.shell }}"
      - "Home: {{ current_user.home_dir }}"
      - "SSH enabled: {{ current_user.ssh_enabled }}"
      - "SSH keys: {{ current_user.ssh_keys | length }}"
      - "Created: {{ user_creation.changed | default(false) }}"
      - "Shell updated: {{ shell_update.changed | default(false) }}"
  when: ansible_verbosity >= 1
