---
- ansible.builtin.apt:
    name: {{ DEB_pkgs }}
    state: latest
    update_cache: 'yes'
  name: Insure required applications are installed
- ansible.builtin.shell:
    cmd: curl -sS https://download.spotify.com/debian/pubkey_7A3A762FAFD4A51F.gpg | sudo gpg --dearmor --yes -o /etc/apt/trusted.gpg.d/spotify.gpg
  name: Install Spotify Repo GPG key
- ansible.builtin.shell:
    cmd: echo "deb http://repository.spotify.com stable non-free" | sudo tee /etc/apt/sources.list.d/spotify.list
  name: Add spotify repo to package manager
- ansible.builtin.apt:
    name: spotify-client
    state: latest
    update_cache: 'yes'
  name: Install Spotify client
