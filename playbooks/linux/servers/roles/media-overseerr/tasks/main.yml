---
# tasks file for media-overseerr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Overseerr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ overseerr_port }}"
      protocol: tcp
      comment: "expose port for overseerr ui http"

- name: Pull the latest Overseerr image
  containers.podman.podman_image:
    name: "{{ overseerr_image }}"
  register: overseerr_image_pull

- name: Stop and remove existing Overseerr container
  containers.podman.podman_container:
    name: "{{ overseerr_container_name }}"
    state: absent
  when: overseerr_image_pull.changed

# Media Request App
- name: Start Overseerr container
  containers.podman.podman_container:
    name: "{{ overseerr_container_name }}"
    hostname: "{{ overseerr_hostname }}"
    image: "{{ overseerr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ overseerr_port }}:{{ overseerr_port }}"
    volumes: "{{ overseerr_volumes }}"
    env: "{{ overseerr_environment }}"
