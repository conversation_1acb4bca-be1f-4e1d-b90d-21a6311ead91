# This should be limited to the hosts that are going to be the web app.
# https://docs.djangoproject.com/en/3.2/ref/settings/#allowed-hosts
NAUTOBOT_ALLOWED_HOSTS=*
NAUTOBOT_BANNER_TOP="Local"
NAUTOBOT_CHANGELOG_RETENTION=0
NAUTOBOT_CONFIG=/opt/nautobot/nautobot_config.py
NAUTOBOT_DB_HOST=db
NAUTOBOT_DB_NAME=nautobot
NAUTOBOT_DB_USER=nautobot
NAUTOBOT_DEBUG=True
NAUTOBOT_DJANGO_EXTENSIONS_ENABLED=False
NAUTOBOT_DJANGO_TOOLBAR_ENABLED=False
NAUTOBOT_HIDE_RESTRICTED_UI=True
NAUTOBOT_LOG_LEVEL=WARNING
NAUTOBOT_METRICS_ENABLED=True
NAUTOBOT_NAPALM_TIMEOUT=5
NAUTOBOT_MAX_PAGE_SIZE=0

# Postgres Container
POSTGRES_USER=${NAUTOBOT_DB_USER}
POSTGRES_DB=${NAUTOBOT_DB_NAME}

# NAUTOBOT REDIS SETTINGS
# When updating NAUTOBOT_REDIS_PASSWORD, make sure to update the password in
# the NAUTOBOT_CACHEOPS_REDIS line as well!
#
NAUTOBOT_REDIS_HOST=redis
NAUTOBOT_REDIS_PORT=6379
# Uncomment REDIS_SSL if using SSL
# NAUTOBOT_REDIS_SSL=True

# Needed for MySQL, should match the values for Nautobot above
MYSQL_USER=nautobot
MYSQL_DATABASE=nautobot

# LDAP environment variables
NAUTOBOT_AUTH_LDAP_SERVER_URI="changeme"
NAUTOBOT_AUTH_LDAP_BIND_DN="changeme"
NAUTOBOT_AUTH_LDAP_BIND_PASSWORD="changeme"
