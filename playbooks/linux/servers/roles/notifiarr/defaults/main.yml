---
# defaults file for notifiarr

# NFS configuration
nfs_server: "192.168.28.31"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# NFS mounts list for automated mounting
nfs_mounts:
  - name: "Media NFS volume"
    src: "{{ nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Main media storage mount"
  - name: "docker-data Config NFS volume"
    src: "{{ nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Container configuration storage mount"

# Local paths
notifiarr_config_dir: "/mnt/docker-app-configs/notifiarr/config"
notifiarr_config_backups: "{{ config_mount_path }}/host-{{ ansible_hostname }}/notifiarr/config"

# Application paths for directory creation
app_paths:
  - path: "{{ notifiarr_config_dir }}"
    comment: "Application config local for speed"
  - path: "{{ notifiarr_config_backups }}"
    comment: "Application config backup dir on NFS mount"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
notifiarr_image: "docker.io/golift/notifiarr:latest"
notifiarr_port: 5454
notifiarr_container_name: "notifiarr"
notifiarr_hostname: "notifiarr"

# Container volumes
notifiarr_volumes:
  - "{{ notifiarr_config_dir }}:/config"
  - "{{ media_mount_path }}:/data"

# Container environment variables
# See: https://hub.docker.com/r/golift/notifiarr
notifiarr_environment:
  DN_API_KEY: "1247d8bf-bb79-4fa3-b5fb-2eb55b3f3258" # Global API key from Notifiarr website (not the client)
  DN_LIDARR_0_NAME: "lidarr"
  DN_LIDARR_0_URL: "http://*************:8686"
  DN_LIDARR_0_API_KEY: "8548a3e35b9e48c88a4c659bdb49c479" # API key generated from the starr application general APIkey
  DN_OVERSEERR_0_NAME: "overseerr"
  DN_OVERSEERR_0_URL: "http://*************:7878"
  DN_OVERSEERR_0_API_KEY: "MTc1MTMzMDQ2NTQ0MTlhM2MwM2Y4LTg3OTctNGRlZi1hZWEzLTcxZDA1NTk4YWJmMQ=="
  DN_PROWLARR_0_NAME: "prowlarr"
  DN_PROWLARR_0_URL: "http://*************:9696"
  DN_PROWLARR_0_API_KEY: "9dcd383b7a6f4234a28fbbf7c7b678e9"
  DN_RADARR_0_NAME: "radarr"
  DN_RADARR_0_URL: "http://*************:7878"
  DN_RADARR_0_API_KEY: "72a0d90a09bd4539801df8f41add1892"
  DN_SONARR_0_NAME: "sonarr"
  DN_SONARR_0_URL: "http://*************:8989"
  DN_SONARR_0_API_KEY: "293e5edc2d954f22a2111ffeb7013356"
  DN_NZBGET_0_NAME: "nzbget"
  DN_NZBGET_0_URL: "http://*************:6789"
  DN_NZBGET_0_USER: "NZBER"
  DN_NZBGET_0_PASS: "NZBERGET"
#  DN_DELUGE_0_NAME: "Deluge"
#  DN_DELUGE_0_URL: "http://*************:8080"
#  DN_DELUGE_0_API_KEY: ""