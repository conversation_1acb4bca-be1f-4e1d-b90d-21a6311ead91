---
# defaults file for media-lidarr

# NFS configuration
nfs_server: "192.168.28.31"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# NFS mounts list for automated mounting
nfs_mounts:
  - name: "Media NFS volume"
    src: "{{ nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Main media storage mount"
  - name: "docker-data Config NFS volume"
    src: "{{ nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Container configuration storage mount"

# Local paths
lidarr_config_dir: "/mnt/docker-app-configs/lidarr/config"
lidarr_config_backups: "{{ config_mount_path }}/host-{{ ansible_hostname }}/lidarr/config"

# Application paths for directory creation
app_paths:
  - path: "{{ lidarr_config_dir }}"
    comment: "Application config local for speed"
  - path: "{{ lidarr_config_backups }}"
    comment: "Application config backup dir on NFS mount"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
lidarr_image: "lscr.io/linuxserver/lidarr:latest"
lidarr_port: 8686
lidarr_container_name: "lidarr"
lidarr_hostname: "lidarr"

# Container volumes
lidarr_volumes:
  - "{{ lidarr_config_dir }}:/config"
  - "{{ media_mount_path }}:/data"

# Container environment variables
lidarr_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
