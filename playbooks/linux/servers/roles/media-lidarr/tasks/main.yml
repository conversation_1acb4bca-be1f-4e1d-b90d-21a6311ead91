---
# tasks file for media-lidarr

- name: "Mount {{ item.name }}"
  ansible.posix.mount:
    src: "{{ item.src }}"
    path: "{{ item.path }}"
    opts: "{{ item.opts }}"
    state: mounted
    fstype: "{{ item.fstype }}"
  loop: "{{ nfs_mounts }}"

- name: Create local directories
  ansible.builtin.file:
    path: "{{ item.path }}"
    state: directory
    mode: "{{ item.mode | default('0755') }}"
    owner: "{{ container_user_id }}"
    group: "{{ container_group_id }}"
  loop: "{{ app_paths }}"

- name: Configure firewall rules for Lidarr
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ lidarr_port }}"
      protocol: tcp
      comment: "expose port for lidarr ui http"

- name: Pull the latest Lidarr image
  containers.podman.podman_image:
    name: "{{ lidarr_image }}"
  register: lidarr_image_pull

- name: Stop and remove existing Lidarr container
  containers.podman.podman_container:
    name: "{{ lidarr_container_name }}"
    state: absent
  when: lidarr_image_pull.changed

# Music Search App
- name: Start Lidarr container
  containers.podman.podman_container:
    name: "{{ lidarr_container_name }}"
    hostname: "{{ lidarr_hostname }}"
    image: "{{ lidarr_image }}"
    state: started
    restart_policy: "on-failure:5"
    network: bridge
    ports:
      - "{{ lidarr_port }}:{{ lidarr_port }}"
    volumes: "{{ lidarr_volumes }}"
    env: "{{ lidarr_environment }}"
