---
#// tasks file for dhcp-kea

- name: Validate required variables
  ansible.builtin.assert:
    that:
      - kea_api_password is defined
      - primary_ip is defined
      - secondary_ip is defined
      - primary_hostname is defined
      - secondary_hostname is defined
      - network_gateway is defined
      - kea_subnets is defined
      - kea_subnets | length > 0
    fail_msg: "Required variables are not defined. Please check your inventory or playbook variables."
    success_msg: "All required variables are properly defined."

- name: Setup DEB dhcp-kea
  import_tasks: deb.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: Setup RHEL dhcp-kea
  import_tasks: rhel.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'

- name: Ensure KEA configuration directory exists
  ansible.builtin.file:
    path: /etc/kea
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Write kea api auth file
  ansible.builtin.copy:
    content: "{{ kea_api_password }}"
    dest: "{{ kea_api_auth_file }}"
    owner: root
    group: root
    mode: '0644'

- name: Write kea ctrl agent conf file
  ansible.builtin.template:
    src: templates/kea-ctrl-agent.conf.json.j2
    dest: /etc/kea/kea-ctrl-agent.conf
    owner: root
    group: root
    mode: '0644'
  notify: Restart kea-ctrl-agent-service

- name: Write kea subnet configuration files
  ansible.builtin.template:
    src: templates/subnet.conf.json.j2
    dest: "/etc/kea/subnet-{{ kea_subnet.id }}-{{ kea_subnet.name }}.conf"
    owner: root
    group: root
    mode: '0644'
  loop: "{{ kea_subnets }}"
  loop_control:
    loop_var: kea_subnet
  vars:
    subnet: "{{ kea_subnet }}"
  notify: Restart kea-dhcp4-service

- name: Write kea subnet reservation files
  ansible.builtin.template:
    src: templates/subnet-reservations.conf.json.j2
    dest: "/etc/kea/subnet-{{ kea_subnet.id }}-{{ kea_subnet.name }}-reservations.conf"
    owner: root
    group: root
    mode: '0644'
  loop: "{{ kea_subnets }}"
  loop_control:
    loop_var: kea_subnet
  vars:
    subnet: "{{ kea_subnet }}"
  notify: Restart kea-dhcp4-service

- name: Get all subnet main files /etc/kea/subnet* files
  ansible.builtin.find:
    paths: /etc/kea
    patterns: 'subnet-*'
    exclude: "*reservations*"
  register: subnet_files

- name: Remove old subnet configuration files (cleanup)
  ansible.builtin.file:
    path: "{{ item }}"
    state: absent
  loop:
    - "/etc/kea/subnet-29-iot.conf"
    - "/etc/kea/subnet-29-iot-reservations.conf"
  notify: Restart kea-dhcp4-service

- name: Write dhcp4 conf file using include subnets
  ansible.builtin.template:
    src: templates/kea-dhcp4.conf.json.j2
    dest: /etc/kea/kea-dhcp4.conf
    owner: root
    group: root
    mode: '0644'
  notify: Restart kea-dhcp4-service
