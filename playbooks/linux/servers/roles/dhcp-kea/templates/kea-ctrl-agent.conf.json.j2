{
  "Control-agent": {
    "http-host": "{{ ansible_default_ipv4.address|default(ansible_all_ipv4_addresses[0]) }}",
    "http-port": 8000,
    "authentication": {
      "type": "basic",
      "realm": "kea-control-agent",
      "clients": [ {
        "user": "{{ kea_api_username }}",
        "password-file": "{{ kea_api_auth_file}}"
      } ]
    },
    "control-sockets": {
      "dhcp4": {
        "socket-type": "unix",
        "socket-name": "/run/kea/kea4-ctrl-socket"
      },
      "dhcp6": {
        "socket-type": "unix",
        "socket-name": "/run/kea/kea6-ctrl-socket"
      },
      "d2": {
        "socket-type": "unix",
        "socket-name": "/run/kea/kea-ddns-ctrl-socket"
      }
    },
    "hooks-libraries": [],
    "loggers": [
      {
        "name": "kea-ctrl-agent",
        "output_options": [
          {
            "output": "/var/log/kea-ctrl-agent.log",
            "flush": true,
            "maxsize": 204800,
            "maxver": 4,
            #// Force print the left curly brace due to jinja2 templating
            "pattern": "%d{{ "{" }}%y.%m.%d %H:%M:%S.%q} %-5p [%c/%i] %m\n"
          }
        ],
        "severity": "INFO",
        "debuglevel": 0
      }
    ]
  }
}
