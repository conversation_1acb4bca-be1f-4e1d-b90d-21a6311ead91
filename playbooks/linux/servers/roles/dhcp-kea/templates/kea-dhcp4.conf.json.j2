{
  "Dhcp4": {
    #// 1d lease time global
    "valid-lifetime": 86400,
    #// T1 hard set value
    #//"renew-timer": 900,
    #// T2 hard set value
    #//"rebind-timer": 1800,
    "calculate-tee-times": true,
    #// T1 default is 50% the lease period
    "t1-percent": 0.5,
    #// T2 default is 87.5% the lease period
    "t2-percent": 0.875,
    "interfaces-config": {
      "interfaces": [ "{{ ansible_default_ipv4.interface }}" ]
    },
    #// See https://kea.readthedocs.io/en/latest/arm/dhcp4-srv.html#fine-tuning-dhcpv4-host-reservation
    #// Disable global host reservations
    "reservations-global": false,
    #// reservations are in each subnet
    "reservations-in-subnet": true,
    #// All host reservations are for addresses that do not belong to the dynamic pool range, improves
    "reservations-out-of-pool": true,
    #// How the matching for reservations will be handled.
    "host-reservation-identifiers": [
      "hw-address"
    ],
    "lease-database": {
      "type": "memfile",
      "lfc-interval": 3600,
      "persist": true,
      #// Default "[kea-install-dir]/var/lib/kea/kea-leases4.csv"
      #//"name": "/var/log/kea/kea-leases4.csv",
      "max-row-errors": 100
    },
    "control-socket": {
      "socket-type": "unix",
      "socket-name": "/run/kea/kea4-ctrl-socket"
    },
    "expired-leases-processing": {
      #// 1h, check leases that expired every hour
      "reclaim-timer-wait-time": 3600,
      #// 2d, for the device to come back and use the same IP
      "hold-reclaimed-time": 172800,
      #// Default 100, Process all reclaimed leases at one time
      "max-reclaim-leases": 0,
      #// Default 250ms, how long it can process the reclaim
      "max-reclaim-time": 0
    },
    "option-data": [ {
        #// Default DNS servers for all
        "name": "domain-name-servers",
        "code": 6,
        "data": "*******, *******"
        #//"data": "{{ primary_ip }}, {{ secondary_ip }}"
    } ],
    #// https://kb.isc.org/docs/kea-hook-libraries
    "hooks-libraries": [
      {
        "library": "/usr/lib/{{ ansible_architecture }}-linux-gnu/kea/hooks/libdhcp_lease_cmds.so"
      },
      {
        "library": "/usr/lib/{{ ansible_architecture }}-linux-gnu/kea/hooks/libdhcp_ha.so",
        "parameters": {
          "high-availability": [ {
            "this-server-name": "{{ ansible_hostname }}",
            "mode": "hot-standby",
            "heartbeat-delay": 10000,
            "max-response-delay": 60000,
            "max-ack-delay": 5000,
            "max-unacked-clients": 5,
            "sync-timeout": 60000,
            "peers": [
              {
                "name": "{{ primary_hostname }}",
                "url": "http://{{ primary_ip }}:8000/",
                "role": "primary",
                "basic-auth-user": "{{ kea_api_username }}",
                "basic-auth-password-file": "{{ kea_api_auth_file }}",
              },
              {
                "name": "{{ secondary_hostname }}",
                "url": "http://{{ secondary_ip }}:8000/",
                "role": "standby",
                "basic-auth-user": "{{ kea_api_username }}",
                "basic-auth-password-file": "{{ kea_api_auth_file }}",
              }
            ]
          } ]
        }
      }
    ],
    "loggers": [ {
      "name": "kea-dhcp4",
      "output_options": [
        {
          "output": "stdout",
          "pattern": "%-5p %m\n"
        },
        {
          "output": "/var/log/kea/kea-dhcp4.log",
          "maxsize": 2048000,
          "maxver": 4
        }
      ],
      "severity": "INFO",
      "debuglevel": 0
    } ],
    "subnet4": [
    {% for subnet_file in subnet_files.files %}
  <?include "{{ subnet_file.path }}"?>{% if not loop.last %},{% endif %}{{ "\n" }}
    {% endfor %}
    ],
    "option-def": [ #// Used for the management network unifi APs
      {
        "space": "ubnt",
        "name": "unifi-address",
        "code": 1,
        "type": "ipv4-address"
    } ],
    "client-classes": [ {
      "name": "ubnt",
      #// from: match if (substring(option dhcp.vendor-class-identifier, 0, 4)) = 'ubnt'
      "test": "substring(option[60].hex,0,4) == 'ubnt'",
      "option-data": [
        {
          "space": "dhcp4",
          "name": "vendor-class-identifier",
          "code": 60,
          "data": "ubnt"
        },
        {
          "name": "vendor-encapsulated-options",
          "code": 43
        },
        {
          "space": "ubnt",
          "name": "unifi-address",
          "code": 1,
          "data": "*************"
        }
      ],
      "option-def": [ {
          "name": "vendor-encapsulated-options",
          "code": 43,
          "type": "empty",
          "encapsulate": "ubnt"
        } ]
    } ]
  }
}
