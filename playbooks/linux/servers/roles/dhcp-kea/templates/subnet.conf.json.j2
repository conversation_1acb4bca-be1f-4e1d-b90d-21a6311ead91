{
  #// {{ subnet.description }}
  "id": {{ subnet.id }},
  "subnet": "{{ subnet.subnet }}",
  "option-data": [
    {
      "name": "subnet-mask",
      "code": 1,
      "data": "{{ subnet.subnet_mask }}"
    },
    {
      "name": "routers",
      "code": 3,
      "data": "{{ subnet.gateway }}"
    },
    {
      "name": "domain-name-servers",
      "code": 6,
      "data": "{{ subnet.dns_servers | default(kea_global_config.default_dns_servers) }}"
    }
  ],
  "valid-lifetime": {{ subnet.valid_lifetime | default(kea_global_config.valid_lifetime) }},
  "max-valid-lifetime": {{ subnet.max_valid_lifetime | default(subnet.valid_lifetime | default(kea_global_config.valid_lifetime)) }},
  "pools": [
{% for pool in subnet.pools %}
    {
      "pool": "{{ pool }}"
    }{% if not loop.last %},{% endif %}
{% endfor %}
  ],
  #// IPs here should never be in the dynamic pool range with our settings
  <?include "/etc/kea/subnet-{{ subnet.id }}-{{ subnet.name }}-reservations.conf"?>
}
