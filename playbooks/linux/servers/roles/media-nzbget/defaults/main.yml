---
# defaults file for media-nzbget

# NFS configuration
nfs_server: "192.168.28.31"
media_nfs_path: "/mnt/nasa-p1z2/Media"
media_mount_path: "/mnt/nasa-p1z2-media"
config_nfs_path: "/mnt/nasa-p1z2/docker-data"
config_mount_path: "/mnt/nasa-p1z2-docker-data"

# NFS mounts list for automated mounting
nfs_mounts:
  - name: "Media NFS volume"
    src: "{{ nfs_server }}:{{ media_nfs_path }}"
    path: "{{ media_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Main media storage mount"
  - name: "docker-data Config NFS volume"
    src: "{{ nfs_server }}:{{ config_nfs_path }}"
    path: "{{ config_mount_path }}"
    opts: "rw"
    fstype: "nfs"
    comment: "Container configuration storage mount"

# Local paths
nzbget_config_dir: "/mnt/docker-app-configs/nzbget/config"
local_media_intermediate_path: "/mnt/local-media/usenet/intermediate"
nzbget_config_backups: "{{ config_mount_path }}/host-{{ ansible_hostname }}/nzbget/config"

# Application paths for directory creation
app_paths:
  - path: "{{ nzbget_config_dir }}"
    comment: "Application config local for speed"
  - path: "{{ local_media_intermediate_path }}"
    comment: "Local SSD for intermediate downloading faster"
  - path: "{{ nzbget_config_backups }}"
    comment: "Application config backup dir on NFS mount"

# Container configuration
container_user_id: "1000"
container_group_id: "1000"
nzbget_image: "lscr.io/linuxserver/nzbget:latest"
nzbget_port: 6789
nzbget_container_name: "nzbget"
nzbget_hostname: "nzbget"

# Container volumes
nzbget_volumes:
  - "{{ nzbget_config_dir }}:/config"
  - "{{ media_mount_path }}:/data"
  - "{{ local_media_intermediate_path }}:/data-local/usenet/intermediate"

# Container environment variables
nzbget_environment:
  PUID: "{{ container_user_id }}"
  PGID: "{{ container_group_id }}"
  TZ: "America/Denver"
  NZBGET_USER: "NZBER"
  NZBGET_PASS: "NZBERGET"
