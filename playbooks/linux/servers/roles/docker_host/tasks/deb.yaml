- name: Add docker gpg apt key
  ansible.builtin.apt_key:
    url: https://download.docker.com/linux/debian/gpg
    state: present

- name: Add repository into sources list
  ansible.builtin.apt_repository:
    repo: deb [arch=amd64] https://download.docker.com/linux/debian {{ ansible_lsb.codename }} stable
    state: present
    filename: docker

- name: Install docker ce
  ansible.builtin.apt:
    name:
      - docker-ce
      - docker-ce-cli
      - docker-compose-plugin
      - containerd.io
    state: present
    update_cache: true

- name: Setup docker group to user
  ansible.builtin.user:
    name: krizzo
    groups: "docker"
    append: true

# Pip3 not installed
# - name: Install docker module for python
#   ansible.builtin.pip:
#     name: docker

- name: Restart Docker
  ansible.builtin.service:
    name: docker
    state: restarted
    enabled: true
