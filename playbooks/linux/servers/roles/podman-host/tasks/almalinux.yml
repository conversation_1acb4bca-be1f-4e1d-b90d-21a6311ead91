---
# AlmaLinux-specific tasks for podman-host

- name: Install required packages
  ansible.builtin.dnf:
    name:
      - container-selinux
      - curl
      - gnupg
    state: present
    update_cache: yes

- name: Install Podman and related packages
  ansible.builtin.dnf:
    name:
      - podman
      - buildah
      - skopeo
    state: present
    update_cache: yes

- name: Create podman config directories
  ansible.builtin.file:
    path: "{{ item }}"
    state: directory
    mode: '0755'
  loop:
    - "/etc/containers"
    - "/etc/containers/registries.conf.d"
  become: yes

- name: Configure main registries.conf
  ansible.builtin.template:
    src: registries.conf.j2
    dest: "/etc/containers/registries.conf"
    mode: '0644'
    backup: yes
  become: yes

- name: Configure registry mirrors in conf.d
  ansible.builtin.copy:
    dest: "/etc/containers/registries.conf.d/000-dockerio-ghcrio.conf"
    content: |
      [[registry]]
      prefix = "ghcr.io"
      location = "ghcr.io"

      [[registry]]
      prefix = "docker.io"
      location = "docker.io"
    mode: '0644'
  become: yes

- name: Create user podman config directory
  ansible.builtin.file:
    path: "~/.config/containers"
    state: directory
    mode: '0755'
  become: no

- name: Configure user registry mirrors
  ansible.builtin.template:
    src: registries.conf.j2
    dest: "~/.config/containers/registries.conf"
    mode: '0644'
  become: no