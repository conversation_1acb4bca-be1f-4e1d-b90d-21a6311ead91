---
# Host-specific variables for silmarillion-001 (Primary DHCP Server)

# Server Role
dhcp_server_role: "primary"
kea_ha_role: "primary"

# Network Interface Configuration
static_ip_config:
  ip: "{{ primary_ip }}"
  netmask: "*************"
  gateway: "{{ network_gateway }}"
  dns_servers:
    - "*******"
    - "{{ primary_ip }}"
    - "{{ secondary_ip }}"

# KEA HA Peer Configuration
kea_ha_this_server_name: "{{ primary_hostname }}"
kea_ha_peer_url: "http://{{ secondary_ip }}:8000/"

# Service Priority
kea_service_priority: "primary"

# Backup Configuration
kea_backup_role: "source"
kea_backup_destination: "{{ secondary_ip }}"

# Monitoring Configuration
kea_monitoring_role: "primary"
kea_stats_collection: true

# Performance Configuration
kea_performance_profile: "primary"
kea_lease_processing_priority: "high"

# Logging Configuration
kea_log_destination: "/var/log/kea/kea-dhcp4-primary.log"
kea_audit_log: "/var/log/kea/kea-audit-primary.log"

# Maintenance Windows
maintenance_window:
  day: "Sunday"
  start_time: "02:00"
  duration_hours: 2

# Health Check Configuration
health_check:
  enabled: true
  interval: 30
  timeout: 10
  retries: 3
  endpoints:
    - "http://localhost:8000/status"
    - "unix:///run/kea/kea4-ctrl-socket"
