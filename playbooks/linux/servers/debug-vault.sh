#!/bin/bash
# Debug script to help troubleshoot vault variable issues

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "=== DHCP Vault Debug Script ==="
echo "Working directory: $SCRIPT_DIR"
echo

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "OK" ]; then
        echo -e "${GREEN}[OK]${NC} $message"
    elif [ "$status" = "INFO" ]; then
        echo -e "${BLUE}[INFO]${NC} $message"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}[WARN]${NC} $message"
    else
        echo -e "${RED}[ERROR]${NC} $message"
    fi
}

echo "1. Checking vault file existence..."
if [ -f "group_vars/dhcpServers_vault.yml" ]; then
    print_status "OK" "Vault file exists"
    
    if grep -q "ANSIBLE_VAULT" "group_vars/dhcpServers_vault.yml"; then
        print_status "OK" "Vault file is encrypted"
    else
        print_status "ERROR" "Vault file exists but is not encrypted"
        exit 1
    fi
else
    print_status "ERROR" "Vault file not found: group_vars/dhcpServers_vault.yml"
    exit 1
fi

echo
echo "2. Attempting to decrypt and view vault file..."
echo "   (You will be prompted for the vault password)"

if command -v ansible-vault &> /dev/null; then
    print_status "INFO" "Decrypting vault file..."
    
    if ansible-vault view group_vars/dhcpServers_vault.yml; then
        print_status "OK" "Vault file decrypted successfully"
    else
        print_status "ERROR" "Failed to decrypt vault file - check your password"
        exit 1
    fi
else
    print_status "ERROR" "ansible-vault command not found"
    exit 1
fi

echo
echo "3. Testing variable access with ansible..."

# Create a simple test playbook
cat > /tmp/vault-test.yml << 'EOF'
---
- hosts: localhost
  connection: local
  gather_facts: false
  tasks:
    - name: Show vault variables
      debug:
        msg:
          - "vault_kea_api_password defined: {{ vault_kea_api_password is defined }}"
          - "vault_kea_api_password value: {{ vault_kea_api_password | default('UNDEFINED') }}"
          - "vault_kea_api_username defined: {{ vault_kea_api_username is defined }}"
          - "vault_kea_api_username value: {{ vault_kea_api_username | default('UNDEFINED') }}"
EOF

print_status "INFO" "Running ansible test..."
if ansible-playbook /tmp/vault-test.yml --ask-vault-pass; then
    print_status "OK" "Ansible can access vault variables"
else
    print_status "ERROR" "Ansible cannot access vault variables"
fi

# Cleanup
rm -f /tmp/vault-test.yml

echo
echo "4. Recommendations:"
echo "   - If vault decryption worked but ansible test failed:"
echo "     * Check variable names in vault file match expected names"
echo "     * Ensure vault file is in correct location: group_vars/dhcpServers_vault.yml"
echo "   - If vault decryption failed:"
echo "     * Verify you're using the correct password"
echo "     * Check if vault file was encrypted with different password"
echo "   - Run the main test: ansible-playbook test-vault-vars.yaml --ask-vault-pass"

print_status "INFO" "Debug complete"
