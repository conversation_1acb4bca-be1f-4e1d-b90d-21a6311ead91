#!/usr/bin/env python3

"""
Test script to validate that the init_combined.yaml playbook can load vault variables correctly.
This simulates what <PERSON><PERSON> does when loading the playbook and vault files.
"""

import yaml
import sys
import os

def test_vault_loading():
    """Test loading of vault variables in the playbook context."""
    
    print("========================================")
    print("Testing Vault Variable Loading")
    print("========================================")
    
    # Test 1: Load playbook YAML
    print("\n1. Testing playbook YAML syntax...")
    try:
        with open('init_combined.yaml', 'r') as f:
            playbook_data = yaml.safe_load(f)
        print("✓ Playbook YAML syntax is valid")
    except Exception as e:
        print(f"✗ Playbook YAML error: {e}")
        return False
    
    # Test 2: Load users.yaml
    print("\n2. Testing users.yaml loading...")
    try:
        with open('global_vars/users.yaml', 'r') as f:
            users_data = yaml.safe_load(f)
        print("✓ Users YAML syntax is valid")
        print(f"✓ Found {len(users_data.get('users_list', []))} users configured")
    except Exception as e:
        print(f"✗ Users YAML error: {e}")
        return False
    
    # Test 3: Load vault file
    print("\n3. Testing vault file loading...")
    try:
        with open('group_vars/init_vault.yml', 'r') as f:
            vault_data = yaml.safe_load(f)
        print("✓ Vault YAML syntax is valid")
    except Exception as e:
        print(f"✗ Vault YAML error: {e}")
        return False
    
    # Test 4: Validate required vault variables
    print("\n4. Testing required vault variables...")
    required_vars = [
        'vault_init_aule_password',
        'vault_init_root_password',
        'vault_new_aule_password', 
        'vault_new_root_password',
        'vault_user_passwords'
    ]
    
    missing_vars = []
    for var in required_vars:
        if var in vault_data:
            print(f"✓ {var} is defined")
        else:
            print(f"✗ {var} is missing")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n✗ Missing required variables: {missing_vars}")
        return False
    
    # Test 5: Validate user passwords structure
    print("\n5. Testing user passwords structure...")
    user_passwords = vault_data.get('vault_user_passwords', {})
    if not isinstance(user_passwords, dict):
        print("✗ vault_user_passwords is not a dictionary")
        return False
    
    users_list = users_data.get('users_list', [])
    user_names = [user['name'] for user in users_list]
    
    for user_name in user_names:
        if user_name in user_passwords:
            print(f"✓ Password defined for user: {user_name}")
        else:
            print(f"⚠ No password defined for user: {user_name}")
    
    # Test 6: Simulate variable resolution (like Ansible does)
    print("\n6. Testing variable resolution simulation...")
    try:
        # Simulate how Ansible would resolve variables
        resolved_vars = {
            'ansible_user': 'aule',
            'ansible_ssh_pass': vault_data.get('vault_init_aule_password', 'changeme'),
            'ansible_become_pass': vault_data.get('vault_init_root_password', 'changeme'),
        }
        
        print(f"✓ ansible_user: {resolved_vars['ansible_user']}")
        print(f"✓ ansible_ssh_pass: {'*' * len(resolved_vars['ansible_ssh_pass'])}")
        print(f"✓ ansible_become_pass: {'*' * len(resolved_vars['ansible_become_pass'])}")
        
    except Exception as e:
        print(f"✗ Variable resolution error: {e}")
        return False
    
    # Test 7: Check playbook structure
    print("\n7. Testing playbook structure...")
    if not isinstance(playbook_data, list) or len(playbook_data) == 0:
        print("✗ Playbook should be a list with at least one play")
        return False
    
    play = playbook_data[0]
    required_play_keys = ['name', 'hosts', 'tasks', 'vars_files']
    
    for key in required_play_keys:
        if key in play:
            print(f"✓ Play has required key: {key}")
        else:
            print(f"✗ Play missing required key: {key}")
            return False
    
    # Check vars_files includes vault
    vars_files = play.get('vars_files', [])
    vault_files = [f for f in vars_files if 'vault' in f]
    if vault_files:
        print(f"✓ Vault files in vars_files: {vault_files}")
    else:
        print("✗ No vault files found in vars_files")
        return False
    
    print("\n========================================")
    print("✅ ALL TESTS PASSED!")
    print("========================================")
    print("\nThe playbook should now work correctly with:")
    print("ansible-playbook -e \"hosts=testServers\" init_combined.yaml -k -K -v")
    print("\nIf you want to encrypt the vault file for production:")
    print("ansible-vault encrypt group_vars/init_vault.yml")
    print("Then use: ansible-playbook -e \"hosts=testServers\" init_combined.yaml -k -K --ask-vault-pass -v")
    
    return True

if __name__ == "__main__":
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    success = test_vault_loading()
    sys.exit(0 if success else 1)
