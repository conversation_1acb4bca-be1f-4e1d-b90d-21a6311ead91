---

# Used to update and true up system base configuration such as users, applications, etc...

# Run:
#   CWD: ansible/playbooks/linux/
#   ansible-playbook -e "hosts=testServers" base_setup.yaml

# Set a raspberry pi up using the lite image with custom settings.
# Change the hostname
#  sudo sed -i 's/silmarillion-004/blueberrypi/g' /etc/hosts; sudo hostnamectl set-hostname blueberrypi

- name: Init fresh linux install
  hosts: "{{ hosts | default('testServers') }}"
  gather_facts: true
  become: true

  vars_files:
  - ./global_vars/users.yaml

  tasks:
    - name: Install default packages
      ansible.builtin.import_role:
        name: base_packages

    - name: Configure users
      ansible.builtin.import_role:
        name: users

    - name: Configure sudoers
      ansible.builtin.import_role:
        name: sudoers
