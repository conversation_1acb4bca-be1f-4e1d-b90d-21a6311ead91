#!/usr/bin/env python3
"""
Role YAML Syntax Validation Script
Validates all YAML files in the users and sudoers roles for syntax errors
"""

import yaml
import sys
import os
from pathlib import Path

def validate_yaml_file(file_path):
    """Validate a single YAML file"""
    try:
        with open(file_path, 'r') as file:
            yaml.safe_load(file)
        return True, None
    except yaml.YAMLError as e:
        return False, str(e)
    except Exception as e:
        return False, f"Error reading file: {str(e)}"

def find_yaml_files(directory):
    """Find all YAML files in a directory"""
    yaml_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith(('.yml', '.yaml')):
                yaml_files.append(Path(root) / file)
    return yaml_files

def main():
    """Main validation function"""
    print("=== Role YAML Syntax Validation ===")
    
    script_dir = Path(__file__).parent
    roles_dir = script_dir / "roles"
    
    if not roles_dir.exists():
        print(f"❌ Roles directory not found: {roles_dir}")
        sys.exit(1)
    
    # Roles to validate
    roles_to_check = ['users', 'sudoers']
    
    total_files = 0
    valid_files = 0
    errors = []
    
    for role_name in roles_to_check:
        role_dir = roles_dir / role_name
        if not role_dir.exists():
            print(f"⚠️  Role directory not found: {role_dir}")
            continue
        
        print(f"\n📁 Validating role: {role_name}")
        
        yaml_files = find_yaml_files(role_dir)
        if not yaml_files:
            print(f"   No YAML files found in {role_name}")
            continue
        
        for yaml_file in yaml_files:
            total_files += 1
            relative_path = yaml_file.relative_to(roles_dir)
            
            is_valid, error = validate_yaml_file(yaml_file)
            if is_valid:
                print(f"   ✅ {relative_path}")
                valid_files += 1
            else:
                print(f"   ❌ {relative_path}: {error}")
                errors.append((relative_path, error))
    
    # Summary
    print(f"\n📊 Validation Summary:")
    print(f"   Total files: {total_files}")
    print(f"   Valid files: {valid_files}")
    print(f"   Invalid files: {total_files - valid_files}")
    
    if errors:
        print(f"\n❌ Errors found:")
        for file_path, error in errors:
            print(f"   {file_path}: {error}")
        sys.exit(1)
    else:
        print(f"\n✅ All YAML files are valid!")
        
    # Additional validation for specific patterns
    print(f"\n🔍 Checking for common issues...")
    
    # Check for regex patterns
    regex_files = []
    for role_name in roles_to_check:
        role_dir = roles_dir / role_name
        if role_dir.exists():
            for yaml_file in find_yaml_files(role_dir):
                with open(yaml_file, 'r') as f:
                    content = f.read()
                    if 'regex_replace' in content:
                        regex_files.append(yaml_file.relative_to(roles_dir))
    
    if regex_files:
        print(f"   📝 Files with regex patterns: {len(regex_files)}")
        for file_path in regex_files:
            print(f"      {file_path}")
    else:
        print(f"   📝 No regex patterns found")
    
    print(f"\n✅ Role validation complete!")

if __name__ == "__main__":
    main()
