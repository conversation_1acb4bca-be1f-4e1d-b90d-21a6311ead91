---
# Test playbook to verify vault variable mapping
# Usage: ansible-playbook test-vault-vars.yaml --ask-vault-pass

- name: Test Vault Variable Mapping
  hosts: localhost
  connection: local
  gather_facts: false

  vars:
    # Map vault variables to role variables (same as main playbook)
    kea_api_password: "{{ vault_kea_api_password }}"
    kea_api_username: "{{ vault_kea_api_username | default(kea_api_username) | default('keadm') }}"

  tasks:
    - name: Load group variables
      ansible.builtin.include_vars:
        file: group_vars/dhcpServers.yml

    - name: Display variable status
      ansible.builtin.debug:
        msg:
          - "=== Vault Variable Test ==="
          - "vault_kea_api_password defined: {{ vault_kea_api_password is defined }}"
          - "vault_kea_api_password value: {{ vault_kea_api_password | default('UNDEFINED') }}"
          - "vault_kea_api_username defined: {{ vault_kea_api_username is defined }}"
          - "vault_kea_api_username value: {{ vault_kea_api_username | default('UNDEFINED') }}"
          - ""
          - "=== Mapped Variables ==="
          - "kea_api_password defined: {{ kea_api_password is defined }}"
          - "kea_api_password length: {{ kea_api_password | length if kea_api_password is defined else 'N/A' }}"
          - "kea_api_username value: {{ kea_api_username }}"

    - name: Test variable validation (same as main playbook)
      ansible.builtin.assert:
        that:
          - vault_kea_api_password is defined
          - vault_kea_api_password != "CHANGE_ME_BEFORE_ENCRYPTING"
          - vault_kea_api_password != "testing"
          - vault_kea_api_password | length > 8
        fail_msg: |
          Vault variable validation failed:
          - vault_kea_api_password must be defined
          - vault_kea_api_password cannot be the default value
          - vault_kea_api_password must be longer than 8 characters
          
          Current value: {{ vault_kea_api_password | default('UNDEFINED') }}
        success_msg: "Vault variables are properly configured"

    - name: Test mapped variable access
      ansible.builtin.assert:
        that:
          - kea_api_password is defined
          - kea_api_password == vault_kea_api_password
        fail_msg: "Variable mapping failed - kea_api_password does not match vault_kea_api_password"
        success_msg: "Variable mapping successful"

    - name: Display success message
      ansible.builtin.debug:
        msg:
          - "✅ All vault variable tests passed!"
          - "✅ Variables are properly mapped"
          - "✅ Ready for DHCP deployment"
