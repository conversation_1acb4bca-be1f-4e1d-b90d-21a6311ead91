---

# Used to update and true up system base configuration such as users, applications, etc...

# Run:
#   CWD: ansible/playbooks/linux/
#   ansible-playbook -e "hosts=testServers" base_setup.yaml

# Set a raspberry pi up using the lite image with custom settings.
# Change the hostname
#  sudo sed -i 's/silmarillion-004/blueberrypi/g' /etc/hosts; sudo hostnamectl set-hostname blueberrypi

- name: Init fresh linux install
  hosts: "{{ hosts | default('testServers') }}"
  gather_facts: true
  become: true

  vars_files:
  - ./global_vars/users.yaml

  vars:
    username_lookup: krizzo

  tasks:
    - name: Configure users
      ansible.builtin.import_role:
        name: users

    - name: Get user info
      ansible.builtin.getent:
        database: passwd
        key: "{{ username_lookup }}"
    - name: Debug info
      ansible.builtin.debug:
        var: ansible_facts.getent_passwd
      when: "ansible_facts.getent_passwd.{{ username_lookup }}[5] == '/bin/bash'"
    - name: Debug info
      ansible.builtin.debug:
        var: ansible_facts.getent_passwd
      when: "ansible_facts.getent_passwd.{{ username_lookup }}[5] == '/usr/bin/zsh'"
