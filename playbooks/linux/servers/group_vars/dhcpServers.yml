---
# Group variables for DHCP servers
# These variables are specific to the dhcpServers group

# KEA API Configuration
kea_api_username: "keadm"
kea_api_auth_file: "/etc/kea/kea-api-password"
# Note: kea_api_password is mapped from vault_kea_api_password in the playbook
# The actual password should be defined in group_vars/dhcpServers_vault.yml

# Network Configuration for DHCP HA
primary_ip: "*************"      # silmarillion-001
secondary_ip: "*************"    # silmarillion-002
primary_hostname: "silmarillion-001"
secondary_hostname: "silmarillion-002"
network_gateway: "************"

# Interface Configuration
dhcp_interface: "{{ ansible_default_ipv4.interface }}"

# Logging Configuration
kea_log_level: "INFO"
kea_log_facility: "local7"

# Backup Configuration
kea_backup_enabled: true
kea_backup_retention_days: 30

# Monitoring Configuration
kea_monitoring_enabled: true
kea_stats_enabled: true

# Service Configuration
kea_services:
  - kea-dhcp4-server
  - kea-ctrl-agent
  - kea-dhcp6-server
  - kea-dhcp-ddns-server

# Firewall Configuration (extends role defaults)
additional_firewall_ports:
  - port: 22
    protocol: "tcp"
    comment: "SSH access"
  - port: 53
    protocol: "udp"
    comment: "DNS queries"
  - port: 53
    protocol: "tcp"
    comment: "DNS zone transfers"

# System Configuration
system_timezone: "America/Denver"
ntp_servers:
  - "pool.ntp.org"
  - "time.nist.gov"

# DNS Configuration
dns_forwarders:
  - "*******"
  - "*******"
  - "*******"

# High Availability Configuration Override
kea_ha_config:
  mode: "hot-standby"
  heartbeat_delay: 10000
  max_response_delay: 60000
  max_ack_delay: 5000
  max_unacked_clients: 5
  sync_timeout: 60000
  # Additional HA settings
  send_lease_updates: true
  sync_leases: true
  sync_timeout: 60000

# Lease Database Configuration
kea_lease_database:
  type: "memfile"
  persist: true
  lfc_interval: 3600
  max_row_errors: 100
  name: "/var/lib/kea/kea-leases4.csv"

# Control Socket Configuration
kea_control_socket:
  socket_type: "unix"
  socket_name: "/run/kea/kea4-ctrl-socket"

# DHCP Options Override
dhcp_global_options:
  domain_name: "home.local"
  domain_search: "home.local"
  ntp_servers: "{{ primary_ip }}, {{ secondary_ip }}"

# Performance Tuning
kea_performance:
  multi_threading:
    enable_multi_threading: true
    thread_pool_size: 4
    packet_queue_size: 64
  cache_threshold: 0.25
  cache_max_age: 1000

# Security Configuration
kea_security:
  decline_probation_period: 86400  # 24 hours
  dhcp4o6_port: 6767
  server_tag: "{{ inventory_hostname }}"
