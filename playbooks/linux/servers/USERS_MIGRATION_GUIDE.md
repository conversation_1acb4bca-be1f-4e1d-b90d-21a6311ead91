# Users Configuration Migration Guide

This guide explains the restructuring of the `users.yaml` file to follow the optimized users and sudoers roles structure.

## Overview of Changes

The `global_vars/users.yaml` file has been completely restructured to:
- Follow Ansible best practices
- Align with optimized users and sudoers roles
- Provide comprehensive configuration options
- Improve maintainability and readability

## Before vs After Structure

### Before (Original)
```yaml
users_list:
  - groups: ''
    id: '1985'
    name: krizzo
    shell: /usr/bin/zsh
    ssh: true
    ssh_pub_keys: [...]
    sudo_permission: 'ALL=(ALL) NOPASSWD: ALL'
    sudoer: true
```

### After (Optimized)
```yaml
users_list:
  - name: "krizzo"
    id: "1985"
    shell: "/usr/bin/zsh"
    create_home: true
    ssh: true
    ssh_pub_keys: [...]
    sudoer: true
    sudo_permission: "admin"
    groups: ["sudo", "docker", "users"]
    additional_groups: ["adm", "systemd-journal"]
    environment:
      EDITOR: "vim"
      LANG: "en_US.UTF-8"
    create_directories: true
    requiretty: false
```

## Key Improvements

### 1. **Consistent Field Ordering**
- **name**: User identifier (first field)
- **id**: UID/GID (string format for consistency)
- **shell**: User shell path
- **create_home**: Explicit home directory creation
- **ssh**: SSH access configuration
- **sudoer**: Sudo access flag
- **groups**: Primary groups list
- **environment**: Custom environment variables

### 2. **Enhanced Configuration Options**
- **additional_groups**: Secondary group memberships
- **environment**: Per-user environment variables
- **create_directories**: Standard directory creation
- **requiretty**: TTY requirement for sudo

### 3. **Improved Data Types**
- **groups**: Changed from empty string to proper list
- **id**: Consistent string format
- **name**: Quoted strings for clarity

### 4. **Common Sudo Rules**
- **admin**: Full administrative access
- **developer**: Development tools access
- **media_admin**: Media server management

### 5. **Global Configuration**
Added comprehensive global settings:
- **users_global_settings**: Default configurations
- **users_sudo_templates**: Sudo rule templates
- **users_required_groups**: System group definitions

## Migration Benefits

### Performance Improvements
- **Optimized loops**: Efficient processing with new role structure
- **Batch operations**: Reduced system calls
- **Fact gathering**: Optimized system information collection

### Security Enhancements
- **TTY requirements**: Configurable per user
- **Environment isolation**: Custom environment variables
- **Group management**: Proper group membership handling

### Maintainability
- **Clear structure**: Consistent field ordering
- **Documentation**: Inline comments and descriptions
- **Validation**: Built-in configuration validation

## User-Specific Changes

### krizzo (Primary Admin)
- **Enhanced**: Added environment variables, additional groups
- **Security**: Configured for admin access with proper group memberships
- **SSH**: Multiple SSH keys properly formatted

### srizzo (Family Admin)
- **Improved**: Added TTY requirement for security
- **Environment**: Configured with nano editor preference
- **Groups**: Proper sudo group membership

### lrizzo (Limited Access)
- **Clarified**: No sudo access (removed conflicting sudo_permission)
- **SSH**: Maintained SSH access with proper key format
- **Groups**: Standard user group only

### trizzo & frizzo (Standard Users)
- **Simplified**: Removed conflicting sudo configurations
- **Consistent**: Standard user setup with basic environment
- **Security**: No SSH or sudo access as intended

## Validation

### Automatic Validation
Run the validation playbook to check configuration:
```bash
ansible-playbook validate-users-config.yml
```

### Manual Checks
1. **UID Uniqueness**: All UIDs are unique
2. **Username Uniqueness**: All usernames are unique
3. **SSH Key Format**: All SSH keys use proper format
4. **Sudo Consistency**: Users with sudoer=true have sudo_permission
5. **Shell Paths**: All shell paths are absolute

## Deployment

### Test Deployment
```bash
# Validate configuration
ansible-playbook validate-users-config.yml

# Test with check mode
ansible-playbook dhcpServers.yaml --check --tags users,sudoers

# Test on single host
ansible-playbook dhcpServers.yaml --limit silmarillion-001 --tags users,sudoers
```

### Production Deployment
```bash
# Deploy to all servers
ansible-playbook dhcpServers.yaml --tags users,sudoers

# Or use specific server playbooks
ansible-playbook mediaServers.yaml --tags users,sudoers
```

## Rollback Plan

If issues occur, the original configuration is preserved in git history:
```bash
# View original configuration
git show HEAD~1:playbooks/servers/global_vars/users.yaml

# Rollback if needed
git checkout HEAD~1 -- playbooks/servers/global_vars/users.yaml
```

## Testing Checklist

- [ ] Configuration validation passes
- [ ] All users can be created successfully
- [ ] SSH access works for enabled users
- [ ] Sudo access works for privileged users
- [ ] Shell configurations are applied
- [ ] Environment variables are set
- [ ] Group memberships are correct
- [ ] No conflicts with existing users

## Support

For issues or questions:
1. Run validation playbook for detailed error messages
2. Check role documentation in `roles/users/README.md` and `roles/sudoers/README.md`
3. Review this migration guide for configuration examples
4. Test changes in check mode before applying

## Next Steps

1. **Validate** the new configuration
2. **Test** on a single server first
3. **Deploy** to all servers
4. **Verify** user access and functionality
5. **Document** any custom configurations for future reference
