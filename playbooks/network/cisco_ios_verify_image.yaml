---
- name: Setting flash to check for member {{ outer_item }}
  ansible.builtin.set_fact:
    flash_string: "flash{{ outer_item }}:"
  when: inner_stack_count  >= "2"

- name: Single switch member setting flash
  ansible.builtin.set_fact:
    flash_string: "flash:"
  when: inner_stack_count  == "1"

- name: DEBUG ===> Image to verify
  ansible.builtin.debug:
    msg: "{{ flash_string }}{{ image_filename }}"
  tags: debug

#
# verify image in directory
#
- name: Verify IOS Image
  cisco.ios.ios_command:
    commands: 'verify /md5 {{ flash_string }}{{ image_filename }}'
    wait_for:
      - result[0] contains Done!
      - result[0] contains {{ ios_image_bin_md5 }}
    retries: 1
  vars:
    ansible_command_timeout: 300
