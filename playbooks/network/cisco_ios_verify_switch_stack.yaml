---

#  Upgrade cisco IOS switches to a new version
# Run:
#   CWD: ansible/playbooks/network/
#   ansible-playbook cisco_ios_upgrade.yaml --ask-pass -v --check

# ftp://test:test@*************/images/cisco/ios/c3560cx-universalk9-mz.152-7.E9.bin

- name: Verify bin file
  # hosts: ciscoSwIOS
  # hosts: as01-a00
  hosts: "{{ hosts | default('none') }}" # Templateing hosts requiring specifying the hosts directly.
  gather_facts: false
  connection: network_cli

  vars:
    ## 2960X
    # image_filename: c2960x-universalk9-mz.152-7.E9.bin
    # ios_image_bin_md5: f5440a06d66a8e4343a0990ea2210e60
    ## 3560
    image_filename: c3560cx-universalk9-mz.152-7.E9.bin
    ios_image_bin_md5: 6a7d278346f2f2cca86306f74159d067

    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: cisco.ios.ios
    ansible_become: true
    ansible_become_method: enable

  tasks:
    - name: Gather facts
      cisco.ios.ios_facts:
        gather_subset: hardware

    - name: DEBUG ===> All ansible facts gathered
      ansible.builtin.debug:
        msg: "facts: {{ ansible_facts }}"
      tags: debug

    - name: DEBUG ===> Stack member count
      ansible.builtin.debug:
        msg: "stacks: {{ ansible_facts.net_stacked_models | length }}"
      tags: debug

    - name: Set stack count
      ansible.builtin.set_fact:
        stack_count: "{{ ansible_facts.net_stacked_models | length }}"
        stack_count_array: []

    - name: Populate stack count array
      ansible.builtin.set_fact:
        stack_count_array: "{{ stack_count_array + [item] }}"
      with_sequence: start=1 end={{ stack_count }}

    - name: DEBUG ===> stack array count
      ansible.builtin.debug:
        msg: "SCA: {{ stack_count_array }}"
      tags: debug

    - name: Loop through stack members
      ansible.builtin.include_tasks: cisco_ios_verify_image.yaml
      loop: "{{ stack_count_array }}"
      vars:
        inner_stack_count: "{{ stack_count }}"
      loop_control:
        loop_var: outer_item
