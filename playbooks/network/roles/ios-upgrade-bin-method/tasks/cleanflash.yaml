#
# variables from outer loop:
# clean_stack_array: "{{ inner_stack_array }}"
# clean_stack_count: "{{ inner_stack_count }}"
# clean_boot_vars: "{{ boot_vars }}"
# loop_var: clean_stack_id
#

- name: Set flash for stack member
  ansible.builtin.set_fact:
    flash_string: "flash{{ outer_item }}:"
  when: inner_stack_count  >= "2"

- name: Set flash for single member stack
  ansible.builtin.set_fact:
    flash_string: "flash:"
  when: inner_stack_count  == "1"

- name: "Get dir of {{ flash_string }}"
  cisco.ios.ios_command:
    commands: "dir {{ flash_string }}"
  register: clean_flash_dir

- name: Flash output to clean
  ansible.builtin.debug:
    msg: "FLASH {{ clean_flash_dir.stdout_lines[0] }}"
  tags: debug

- name: Init ignore list
  ansible.builtin.set_fact:
    ignorelist: []

- name: Add show ver image to ignore list
  ansible.builtin.set_fact:
    ignorelist: "{{ ignorelist + clean_boot_vars.stdout_lines[0] }}"

- name: Add show boot image to ignore list
  ansible.builtin.set_fact:
    ignorelist: "{{ ignorelist + clean_boot_vars.stdout_lines[1] }}"

- name: Complete ignore list
  ansible.builtin.debug:
    msg: "IGNORE {{ ignorelist }}"
  tags: debug

- name: Filter out ignore list files and all non platform files
  ansible.builtin.set_fact:
    del_files: "{{ clean_flash_dir.stdout_lines[0] | cleanfile(ignorelist, ios_platform) }}"
  debugger: on_failed

- name: List of files to be deleted
  ansible.builtin.debug:
    msg: "file(s) to be deleted: {{ del_files }}"
  tags: debug

- name: Delete all unnecessary platform files and directories from flash
  cisco.ios.ios_command:
    commands: "delete /force /recursive {{ flash_string }}{{ item }}"
  vars:
    ansible_command_timeout: 20
  with_items: "{{ del_files }}"
  when: ios_flash_clean_enabled
