---
# tasks file for ios-upgrade-bin-method
- name: Gather facts
  cisco.ios.ios_facts:
    gather_subset: hardware

- name: All hardware facts gathered
  ansible.builtin.debug:
    msg: "facts: {{ ansible_facts }}"
  tags: debug

- name: Stack Member Count
  ansible.builtin.debug:
    msg: "stacks: {{ ansible_facts.net_stacked_models | length }}"
  tags: debug

- name: Set stack count
  ansible.builtin.set_fact:
    stack_count: "{{ ansible_facts.net_stacked_models | length }}"
    stack_count_array: []

- name: Populate stack count array
  ansible.builtin.set_fact:
    stack_count_array: "{{ stack_count_array + [ item ] }}"
  with_sequence: start=1 end={{ stack_count }}

- debug:
    msg: "SCA: {{ stack_count_array }}"
  tags: debug

- name: Loop through stack members
  ansible.builtin.include_tasks: verify.yaml
  loop: "{{ stack_count_array }}"
  vars:
    inner_stack_count: "{{ stack_count }}"
    inner_stack_count_array: "{{ stack_count_array }}"
  loop_control:
    loop_var: outer_item
