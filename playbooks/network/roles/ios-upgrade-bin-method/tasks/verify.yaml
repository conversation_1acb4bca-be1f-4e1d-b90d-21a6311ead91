#
# we need to know if we are a stack
# only stacks have flash1: and flash2: ....
#
# vars from outer:
# inner_stack_count, inner_stack_array, outer_item
#

- name: Set flash for stack member
  ansible.builtin.set_fact:
    flash_string: "flash{{ outer_item }}:"
  when: inner_stack_count  >= "2"

- name: Set flash for single member stack
  ansible.builtin.set_fact:
    flash_string: "flash:"
  when: inner_stack_count  == "1"

- name: "Get dir of {{ flash_string }}"
  cisco.ios.ios_command:
    commands: "dir {{ flash_string }}"
  register: flash_dir

- name: "Get boot and running IOS as vars"
  cisco.ios.ios_command:
    commands:
      - "show version | include {{ ios_show_version_image }}"
      - "show boot | include {{ ios_show_boot_path_list }}"
  register: boot_vars

- name: Boot and running ios vars
  ansible.builtin.debug:
    msg: "boot vars: {{ boot_vars }}"
  tags: debug

- name: Looking for any dir that may contain the image
  ansible.builtin.debug:
    msg: "image: {{ ios_image_string }}, flashdir: {{ flash_dir.stdout[0] }}"
  tags: debug

- name: Possible Image Found
  ansible.builtin.set_fact:
    image_ready: "{{ true if (flash_dir.stdout[0] | regex_search('.*' + ios_image_string + '.*')) else false }}"
    image_path: "{{ flash_string }}{{ ios_image_string }}/{{ ios_image_string }}.bin"

- name: Image found and ready
  ansible.builtin.debug:
    msg: "image ready: {{ image_ready }}"
  tags: debug

#
# verify enough space left on flash on every stack member
# if we have to upload new image
# loop into cleanflash for every stack member
#
- name: Verify space available on flash(s) to copy new image
  ansible.builtin.include_tasks: cleanflash.ymal
  loop: "{{ inner_stack_count_array }}"
  vars:
    clean_stack_array: "{{ inner_stack_array }}"
    clean_stack_count: "{{ inner_stack_count }}"
    clean_boot_vars: "{{ boot_vars }}"
  loop_control:
    loop_var: clean_stack_id
  when: not image_ready and outer_item == "1"

#
# start uploading image if
# image is NOT on stack member 1
# #
# - name: start uploading image
#   ansible.builtin.include_tasks: upload.yml
#   when: not image_ready and outer_item == "1"

- name: Get updated boot and running IOS variables again after uploading image
  cisco.ios.ios_command:
    commands:
      - "show version | include {{ ios_show_version_image }}"
      - "show boot | include {{ ios_show_boot_path_list }}"
  register: boot_vars_2
  when: (not image_ready) and outer_item == "1"

- name: Set boot vars
  ansible.builtin.set_fact:
    boot_vars: boot_vars2
  when: (not image_ready) and outer_item == "1"

- ansible.builtin.debug:
    msg: "{{ boot_vars }}"
  tags: debug

#
# we only reach this if something is wrong
#
- name: Fail if image is missing on stack members other than master
  ansible.builtin.fail:
    msg: "IOS image is missing on stack member {{ outer_item }}. Something is wrong."
  when: (not image_ready) and (outer_item != 1)

#
# verify image in directory
#
- name: "verify IOS Image: {{ image_path }}"
  cisco.ios.ios_command:
    commands: 'verify /md5 {{ image_path }}'
    wait_for:
      - result[0] contains Done!
      - result[0] contains {{ ios_image_md5 }}
    retries: 1
  vars:
    ansible_command_timeout: 300
  when: image_ready

#
# verify boot variable set when we are in loop stack master
#

- ansible.builtin.set_fact:
    calculated_image: "flash:/?{{ ios_image_string }}/{{ ios_image_string }}.bin"
    boot_image: "{{ boot_vars.stdout_lines[1][(outer_item | int) - 1] }}"

- ansible.builtin.debug:
    msg: "boot image: {{ boot_image }} - calculated image: {{ calculated_image }}"
  tags: debug

- name: fail if boot variable not set correctly
  ansible.builtin.fail:
    msg: device boot variable differs from uploaded image
  when: not boot_image | regex_search(calculated_image)
