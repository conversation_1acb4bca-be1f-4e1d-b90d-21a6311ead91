# Filter
# this plugin takes 2 parameters: ignorelist and platform

import re
from ansible.errors import AnsibleFilterError


def _validate_platform_specific_file(file, platform):
    """
    Validate if the given file is specific to the provided platform.

    :param file: str, the file name to validate
    :param platform: str, the target platform (e.g., 'c2960x', 'c3560cx', 'darwin', 'linux', etc.)
    :return: bool, True if the file is specific to the platform, False otherwise
    """
    pattern = re.compile(r"^{}[-\w\.]*$".format(platform), re.IGNORECASE)
    return bool(pattern.match(file))


def _trim_ignorelist_directory(ignorelist):
    """
    Splits the ignorelist and returns the first part of the split string.

    :param ignorelist: A list of strings to be split
    :return: A list of the first parts of the split strings
    """
    result = []
    for ignore in ignorelist:
        try:
            split_string = re.split(r"^(.*flash.?:/?).*", ignore, flags=re.IGNORECASE)[
                1
            ]
            result.append(split_string.strip("/"))
        except IndexError:
            raise AnsibleFilterError(
                "Unexpected IgnoreList match. Check show version and show boot output."
            )
    return result


# is file matching to ignorenlist
# only the beginning must match (include .bin and subdirs)
def _ignorelist_check(file, ignorelist):
    """
    Check if a file is in the ignorelist.

    Parameters:
    file (str): The file name to check.
    ignorelist (list): A list of ignore patterns.

    Returns:
    bool: True if the file is not in the ignorelist, False otherwise.
    """
    for ignore_entry in ignorelist:
        if re.fullmatch(ignore_entry, file):
            return False
    return True


def cleanfile(filelist, ignorelist, platform):
    """
    Filter a file list based on an ignorelist and platform.

    This function filters a list of files based on an ignorelist and platform.
    It checks if each file in the filelist is present in the ignorelist and
    if it is specific to the given platform. If both conditions are met,
    the file is added to the result list.

    Parameters:
    filelist (list): A list of files to filter.
    ignorelist (list): A list of ignore patterns.
    platform (str): The platform to filter the files for (2960X, C3560CX)

    Returns:
    list: A list of files that are not in the ignorelist and are specific
    to the given platform.
    """

    if not filelist or not ignorelist or not platform:
        raise AnsibleFilterError("FileList, IgnoreList, Platform must not be empty.")
    if not isinstance(filelist, list):
        raise AnsibleFilterError("FileList must be an array")
    if not isinstance(ignorelist, list):
        raise AnsibleFilterError("IgnoreList must be an array")

    result = []
    ignorelist_trimed = _trim_ignorelist_directory(ignorelist)

    for line in filelist:
        regex = r"[^ ]*$"
        matches = re.search(regex, line)
        filematch = matches.group()
        if _validate_platform_specific_file(filematch, platform):
            if _ignorelist_check(filematch, ignorelist_trimed):
                result.append(filematch)
    return result


class FilterModule(object):
    """Clean File filters"""

    def filters(self):
        """
        Define the filters provided by this module.

        Returns:
        dict: A dictionary of filter names and their corresponding functions.
        """
        return {
            "cleanfile": cleanfile,
        }
