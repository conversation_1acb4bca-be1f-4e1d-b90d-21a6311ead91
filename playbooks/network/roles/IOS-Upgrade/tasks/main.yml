---
## Check IOS Version
- name: Checking current version
  cisco.ios.ios_facts:

- ansible.builtin.debug:
    msg:
    - "Current version: {{ ansible_net_version }}"
    - "Upgrade Version: {{ upgrade_ios_version }}"
    - "Switch is not compliant and will be upgraded"
  when: ansible_net_version != upgrade_ios_version

- ansible.builtin.set_fact:
    number_of_switch_members: "{{ ansible_net_stacked_serialnums | length }}"

- ansible.builtin.debug:
    msg:
    - "Number of stacked members: {{ number_of_switch_members }}"

## Backup Running Config requires absolute path not shortcuts such as ~/
- name: Backup running config and save to start
  cisco.ios.ios_config:
    backup: true
    backup_options:
      dir_path: "customers/{{ customer_name }}{{ network_files_dir }}{{ backups_dir }}{{ inventory_hostname }}"
      filename: "config-{{ DTG }}.txt"
    save_when: always

- name: Check SCP server enabled
  cisco.ios.ios_command:
    commands: 'show run | inc scp server'
  register: scp_server_status

## Enable SCP on the switch temporarlly and will disable after the file gets uploaded
- name: Enable SCP service skipped if scp was already enabled
  cisco.ios.ios_config:
    lines:
     - ip scp server enable
  when: scp_server_status.stdout[0] | length == 0

## SINGLE: Copy software to target device and verify
- name: Single Switch Upgrade
  block:
    # This will upload even in check mode
    # - name: Copy Image // This could take a while timeout set to 1800s (30m)
    #   ansible.netcommon.net_put:
    #     src: "customers/{{ customer_name }}{{ network_files_dir }}{{ images_dir }}{{ image_file }}"
    #     dest: "flash:/{{ image_file }}"
    #   vars:
    #     ansible_command_timeout:  1800

    - name: Verify uploaded image
      cisco.ios.ios_command:
        commands: verify /md5 flash:{{ image_file }}
      register: command_output_results

    - ansible.builtin.set_fact:
        remote_image_hash: "{{ command_output_results.stdout[0] | regex_search(regexp,'\\1') | first }}"
      vars:
        regexp: '.* = ([a-f0-9]{32})$'

    - name: ASSERT THAT THE IOS FILE HASH IS CORRECT
      assert:
        that:
          - local_image_hash == remote_image_hash

    - ansible.builtin.debug:
        msg:
        - "File transfer has been successfully completed"
        - "Local image hash: {{ local_image_hash }}"
        - "Remote image hash: {{ remote_image_hash }}"

    # ## Change the Boot Variable to the new image for a single switch
    # - name: Change Boot Variable to new image
    #   cisco.ios.ios_config:
    #     commands:
    #       - "boot system flash:{{ image_file }}"
    #     save_when: always
    #   when: number_of_switch_members | int == 1
  when: number_of_switch_members | int == 1

  ## TODO UNTESTED
  # ## STACKED: Copy image to the first switch flash1 in a stack, this will upload even in check mode
  # - name: Stacked Switch Upgrade
  #   block:
  #     - name: Copy Image to first switch member // This could take a while timeout set to 1800s (30m)
  #       ansible.netcommon.net_put:
  #         src: "customers/{{ customer_name }}{{ network_files_dir }}{{ images_dir }}{{ image_file }}"
  #         dest: "flash1:/{{ image_file }}"
  #       vars:
  #         ansible_command_timeout:  1800

  #     ## STACKED: Copy the image file from stack member 1 to the others
  #     - name: Copy image file over to other switch stack members
  #       cisco.ios.ios_command:
  #         commands: "copy flash1:/{{ image_file }} flash{{ item | int + 1 }}:/{{ image_file }}"
  #       with_sequence: count={{ number_of_switch_members | int - 1 }}

  #     - ansible.builtin.debug:
  #         msg: "commands: copy flash1:/{{ image_file }} flash{{ item | int + 1 }}:/{{ image_file }}"
  #       with_sequence: count={{ number_of_switch_members | int - 1 }}

    #TODO Need to store each of these as a map to verify the switch member and image hash.
    ##  STACKED: Verify that the copy to each switch stack members flash was successful
    # - name: Verify uploaded image
    #   cisco.ios.ios_command:
    #     commands: verify /md5 flash:{{ image_file }}
    #   register: command_output_results
    #   when: number_of_switch_members | int > 1
    # - ansible.builtin.set_fact:
    #     remote_image_hash: "{{ command_output_results | regex_search(regexp,'\\1') | first }}"
    #   vars:
    #     regexp: '.* = ([a-f0-9]{32})$'
    #   when: number_of_switch_members | int > 1

    ## TODO Change the boot var of a single switch member and realod that member.
    # ## Change the Boot Variable to the new image for a single switch
    # - name: Change Boot Variable to new image
    #   ios_config:
    #     commands:
    #       - "boot system flash:{{ image_file }}"
    #     save_when: always
    #   when: number_of_switch_members | int == 1
  # when: number_of_switch_members | int > 1

## Disable SCP on the switch
- name: Disable SCP service skipped if scp was already enabled
  cisco.ios.ios_config:
    lines:
      - no ip scp server enable
  when: scp_server_status.stdout[0] | length == 0

## TODO Reload should happen on individual switch members where possible.
# ## Reload the device
# - name: Reload the Device
#   cli_command:
#     command: reload
#     prompt:
#       - confirm
#     answer:
#       - 'y'

# ## Wait for the device to reload
# - name: Wait for device to come back online
#   wait_for:
#     host: "{{ inventory_hostname }}"
#     port: 22
#     delay: 90
#   delegate_to: localhost

# ## Check current image
# - name: Check Image Version
#   ios_facts:

# - ansible.builtin.debug:
#     msg:
#     - "Current version is {{ ansible_net_version }}"

# - name: ASSERT THAT THE IOS VERSION IS CORRECT
#   assert:
#     that:
#       - upgrade_ios_version == ansible_net_version
# - ansible.builtin.debug:
#     msg:
#     - "Software Upgrade has been successfully completed"
