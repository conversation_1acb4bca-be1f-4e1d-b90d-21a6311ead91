---
#  Backup configuration of cisco switches
#  Keep only the last 2 that were older than 5 mins (300s)

# Run:
#   CWD: ansible/playbooks/network/
#   ansible-playbook cisco_ios_config_backup.yaml --ask-pass -v
#   ansible-playbook -l "smiCiscoIOS" cisco_ios_config_backup.yaml --ask-pass
#   ansible-playbook -i 'hosts_smi.yaml' -e "hosts=smiCiscoIOS" cisco_ios_config_backup.yaml --ask-pass
#   ansible-playbook -i 'hosts_smi.yaml' -e "hosts=AZ-AHWMSW01CISC,AZ-ANTMSW01CISC,AZ-ANTMSW02CISC,AZ-ANTVGW01CISC" cisco_ios_config_backup.yaml --ask-pass --check

- name: Gathering backup of switches
  # hosts: all
  hosts: "{{ hosts | default('none') }}" # Templateing hosts requiring specifying the hosts directly.
  gather_facts: false
  connection: network_cli

  vars:
    # Path relative to this file for backup
    customer_files_dir: "customer_files/"
    customer_name_dir: "invite/"
    network_files_dir: network-devices/
    backups_dir: backups/

    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: cisco.ios.ios
    ansible_become: true
    ansible_become_method: enable

    # Keep only the last 2 + 1 backups and the current one.
    num_backups_to_keep: 2

  tasks:
      # With strategy free meaning run as quickly (many) as possible run_once may cause oddities.
      # Such as different time depending on each playbook run.
      # This is not an issue as it's only used for dating the config file names.
    - name: Store date/time as fact
      delegate_to: localhost
      ansible.builtin.set_fact:
        DTG: "{{ now(utc=true, fmt='%Y%m%dT%H%M%SUTC%z') }}"
      run_once: true

    - name: Backup config
      cisco.ios.ios_config:
        backup: true
        backup_options:
          dir_path: "{{ customer_files_dir }}{{ customer_name_dir }}{{ network_files_dir }}{{ backups_dir }}{{ inventory_hostname }}"
          filename: "config-{{ DTG }}.txt"
        save_when: always

    - name: Get list of backups
      ansible.builtin.find:
        paths: "{{ customer_files_dir }}{{ customer_name_dir }}{{ network_files_dir }}{{ backups_dir }}{{ inventory_hostname }}"
        file_type: file
        age: 300   # Only consider files modified older than this
      register: file_list

    - name: Sort files by modification time
      ansible.builtin.set_fact:
        sorted_files: "{{ file_list.files | sort(attribute='mtime', reverse=true) }}"

    - name: Delete files older than the Nth newest
      ansible.builtin.file:
        path: "{{ item.path }}"
        state: absent
      with_items: "{{ sorted_files[num_backups_to_keep:] }}"
