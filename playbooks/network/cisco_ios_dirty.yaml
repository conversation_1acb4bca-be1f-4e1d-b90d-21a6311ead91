---
#  Push false files to cisco switch for cleanup testing
# Run:
#  ansible-playbook -e "hosts=LAB-SW2" cisco_ios_dirty.yaml --ask-pass -v

- name: Gathering backup of switches
  # hosts: all
  hosts: "{{ hosts | default('none') }}" # Templateing hosts requiring specifying the hosts directly.
  gather_facts: false
  connection: network_cli

  vars:
    # Path relative to this file for backup
    customer_files_dir: "customer_files/"
    customer_name_dir: "invite/"
    network_files_dir: network-devices/
    backups_dir: backups/

    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: cisco.ios.ios
    ansible_become: true
    ansible_become_method: enable

    # Keep only the last 2 backups and the current one.
    num_backups_to_keep: 2

  tasks:

    #### SCP ONLY ####
    - name: Check SCP server status
      cisco.ios.ios_command:
        commands: 'show run | inc scp server'
      register: scp_server_status

    #### SCP ONLY ####
    - name: Enable SCP service if not enabled
      cisco.ios.ios_config:
        lines:
          - ip scp server enable
      when: scp_server_status.stdout[0] | length == 0

    #### SCP ONLY ####
    - name: Copy Image to main switch member // This could take a while timeout set to 3600s (1h)
      ansible.netcommon.net_put:
        src: "images/false_files_test/{{ item }}"
        dest: flash:/{{ item }}
      loop:
        - "c3560cx-universalk9-mz.152-3.E.bin"
        - "c3560cx-universalk9-mz.152-7.E7.bin"
        - "c3560cx-universalk9-tar.152-3.E.tar"
        - "c3560cx-universalk9-tar.152-7.E7.tar"
      vars:
        ansible_command_timeout: 3600 #  One hour for copying the image
      tags:
        - upload

    #### SCP ONLY ####
    - name: Disable SCP service if it wasn't on before
      cisco.ios.ios_config:
        lines:
          - no ip scp server enable
      when: scp_server_status.stdout[0] | length == 0
