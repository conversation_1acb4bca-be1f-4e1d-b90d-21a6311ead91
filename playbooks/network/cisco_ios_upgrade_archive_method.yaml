---
#  Upgrade cisco IOS switches to a new version
# Run:
#   CWD: ansible/playbooks/network/
#   ansible-playbook -l "" cisco_ios_upgrade_archive_method.yaml --ask-pass -v
#   ansible-playbook -i 'hosts_smi.yaml' -e "ansible_user=YOURE_USER_NAME"-e "hosts=smiCiscoIOS" cisco_ios_upgrade_archive_method.yaml --ask-pass -v
#   ansible-playbook -i 'hosts_smi.yaml' -e "hosts=LAB-SW2" cisco_ios_upgrade_archive_method.yaml --ask-pass

- name: Upgrade Cisco IOS
  hosts: "{{ hosts | default('none') }}" # Templateing hosts requiring specifying the hosts directly.
  gather_facts: false
  connection: network_cli

  vars:
    customer_name_dir: "invite/"

    protocol: "tftp"
    server: "*************"
    # server: "***********"

    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: cisco.ios.ios
    ansible_become: true
    ansible_become_method: enable

  tasks:
      # With strategy free meaning run as quickly (many) as possible run_once may cause oddities.
      # Such as different time depending on each playbook run.
      # This is not an issue as it's only used for dating the config file names.
    - name: Store date/time as fact
      delegate_to: localhost
      ansible.builtin.set_fact:
        DTG: "{{ now(utc=true, fmt='%Y%m%dT%H%M%SUTC%z') }}"
      run_once: true

    - name: Upgrade IOS images via archive download-sw command
      ansible.builtin.import_role:
        name: ios-upgrade-archive-method
