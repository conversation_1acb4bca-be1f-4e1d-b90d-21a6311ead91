---
# Upgrade cisco IOS with cleaning up flash as well.
# Run:
#  ansible-playbook -e "hosts=LAB-SW2" cisco_ios_upgrade.yaml --ask-pass -v


- hosts: LAB-SW2
  connection: network_cli
  gather_facts: false
  roles:
    - maxrainer.cisco_iosupgrade
  vars:
    - ios_platform: c3560cx

    - ios_upload_protocol: ftp
    - ios_upload_username: tester
    - ios_upload_password: test
    - ios_upload_server: *************
    - ios_path_local: "/images/cisco/ios/"

    # - ios_image_string: c3560cx-universalk9-mz.152-7.E9
    # - ios_image_md5: 6a7d278346f2f2cca86306f74159d067
    # - ios_image_tar: c3560cx-universalk9-tar.152-7.E9.tar
    # - ios_image_tar_md5: 7c71b880aca7d53a7e540092822eaeb8
    - ios_image_string: c3560cx-universalk9-mz.152-4.E2
    - ios_image_md5: 3ce4eaf3df294984bd85cbb741b30b52
    - ios_image_tar: c3560cx-universalk9-tar.152-4.E2.tar
    - ios_image_tar_md5: 3ce4eaf3df294984bd85cbb741b30b52

    - ios_flash_clean_enabled: true
    #  cleanup flash required string does a regex to parse the list of files in flash
    - ios_platform: c3560cx
    - ios_verify_timeout: 90
    - debug: true

    - ansible_connection: ansible.netcommon.network_cli
    - ansible_network_os: cisco.ios.ios
    - ansible_become: true
    - ansible_become_method: enable
