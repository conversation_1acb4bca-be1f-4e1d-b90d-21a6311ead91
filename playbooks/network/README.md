# Ansible
Location for all Ansible CSM playbooks and roles, for each org.

## Setup a collector to run ansible on rocky linux

### On Collector install some software we'll want

`sudo dnf install tmux rsync`

### On Localhost rsync the Ansible repo over excluding specific files

NOTE: THIS IS A ONE WAY

`rsync -rav --exclude '.git' --exclude '.gitignore' --exclude 'customer_files/*' ~/repos/github.com/invite-networks/Ansible username@logicmonitorcollector:~/`

### On Collector setup a user python env to avoid messing with the system one

```bash
sudo dnf -y install epel-release
sudo dnf -y install git gcc zlib-devel bzip2-devel readline-devel sqlite-devel openssl-devel xz-devel libffi-devel
curl https://pyenv.run | bash

# Set up auto loading
tee -a ${HOME}/.bashrc << 'EOF' > /dev/null
export PYENV_ROOT="$HOME/.pyenv"
[[ -d $PYENV_ROOT/bin ]] && export PATH="$PYENV_ROOT/bin:$PATH"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"
EOF

# Reload the shell
exec "$SHELL"

pyenv install 3.12.2
pyenv shell 3.12.2
```

### Setup a virtual env for the project

We install the python packages here instead of using pip3 -r requirements file.

```bash
python3 -m pip install --user pipx
python3 -m pipx ensurepath
pipx install pipenv
cd ${HOME}/Ansible
pipenv install ansible  --python 3.12.2
pipenv shell
pipenv install ansible-lint scp
```

### Install the ansible collections we need to run our playbooks

`ansible-galaxy collection install -r requirements.yaml`

### Verify that the ansible playbooks work

Lets try to backup a configuration from a swtich

`ansible-playbook -i 'hosts_smi.yaml' -e "ansible_user=kyle.rizzo_IT" -e "hosts=AZ-AHWMSW01CISC" cisco_ios_config_backup.yaml --ask-pass`

## Clean up options if things aren't working correctly this is heavy handed

```bash
cd ${HOME}/Ansible
pipenv --rm

pyenv uninstall 3.12.2
```
