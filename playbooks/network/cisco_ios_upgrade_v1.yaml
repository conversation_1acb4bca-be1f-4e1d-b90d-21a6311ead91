---

#  Upgrade cisco IOS switches to a new version
# Run:
#   CWD: ansible/playbooks/network/
#   ansible-playbook cisco_ios_upgrade.yaml --ask-pass -v --check

- name: Upgrade Cisco IOS
  # hosts: ciscoSwIOS
  # hosts: as01-a00
  hosts: LAB-SW2
  gather_facts: false
  connection: network_cli

  vars:
    ## 3850 Cat
    # upgrade_ios_version: "16.12.09"
    # image_file: cat3k_caa-universalk9.16.12.09.SPA.bin

    ## 3560
    upgrade_ios_version: "15.2(7)E9"
    image_file: c3560cx-universalk9-mz.152-7.E9.bin

    # Root dir '/' for ansible is where the placebook exists, this file.
    customer_files_dir: "customer_files/"
    customer_name_dir: "invite/" # This should be an absolute path not a shortcut such as ~/
    network_files_dir: network-devices/
    backups_dir: backups/ # This dir will be created when the backup configs are taken
    images_dir: images/
    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: cisco.ios.ios
    ansible_become: yes
    ansible_become_method: enable

  tasks:
    - name: Store date/time as fact
      delegate_to: localhost
      ansible.builtin.set_fact:
        DTG: "{{ now(utc=true,fmt='%Y%m%dT%H%M%SUTC%z') }}"
      run_once: true

    - debug:
        msg:
          - "Date Time: {{ DTG }}"

    - name:  Get MD5 of "{{ customer_files_dir }}{{ customer_name_dir }}{{ network_files_dir }}{{ images_dir }}{{ image_file }}"
      delegate_to: localhost
      ansible.builtin.stat:
        path: "{{ customer_files_dir }}{{ customer_name_dir }}{{ network_files_dir }}{{ images_dir }}{{ image_file }}"
        checksum_algorithm: md5
      register: FST
      run_once: true

    - ansible.builtin.debug:
        msg: "File hash info: {{ FST }}"

    - name: Store file hash as fact
      delegate_to: localhost
      ansible.builtin.set_fact:
        local_image_hash: "{{ FST.stat.checksum }}"
      run_once: true

    - debug:
        msg:
        - "File Hash: {{ local_image_hash }}"

    - name: Upgrade IOS images
      import_role:
        name: IOS-Upgrade
