---

# Install some basic packages and setup my user, requires python3 is installed

# Run:
#
#   CWD: ansible/playbooks/linux_legacy/
#
#   ansible-playbook -l dhcpServers piDHCP.yaml -v --check
#
#   Update DHCP static files doesn't work because of the dynamic vars
#   ansible-playbook -l dhcpServers piDHCP.yaml --start-at-task="dhcpd : copy_shared_conf_files"

- name: setup_dhcpServers_servers
  become: true
  gather_facts: true
  hosts: dhcpServers

  tasks:
    # - name: configure_base-setup_role
    #   import_role:
    #     name: base-setup
    - name: configure_dhcpd_role
      import_role:
        name: dhcpd
