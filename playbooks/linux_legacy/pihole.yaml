---
# Run:
#   CWD: ansible/playbooks/linux_legacy/
#   ansible-playbook -l vagrantRockyServers -k -u vagrant pihole.yaml --check -v
# or
#   ansible-playbook -l rockyTestServers -kK pihole.yaml --check -v

- name: setup_pihole_on_server
  hosts: silmarillion-003
  gather_facts: yes
  become: no

  tasks:
    - name: run_base_setup
      import_role:
        name: base-setup

    - name: install_docker_run_pihole
      import_role:
        name: pihole
