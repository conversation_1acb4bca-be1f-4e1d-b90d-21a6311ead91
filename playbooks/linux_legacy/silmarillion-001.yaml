---
#   cfddns Install:
#     ansible-playbook -l silmarillion-001 cfddns.yaml -v --check
#   Update Containers:
#     ansible-playbook -l silmarillion-001 cfddns.yaml --start-at-task="cfddns : start_cf_ddns"

- name: setup_silmarillion_001_server
  become: true
  gather_facts: true
  hosts: silmarillion-001

  tasks:
    - name: configure_base-setup_role
      import_role:
        name: base-setup

    - name: configure_docker_host_role
      import_role:
        name: docker-host
      when: ansible_facts['os_family']|lower == 'debian'

    - name: configure_dhcpd_role
      import_role:
        name: dhcpd
