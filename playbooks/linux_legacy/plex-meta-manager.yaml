---
#   Plex Meta Manager Run:
#     ansible-playbook -l plex plex-meta-manager.yaml --check -v
#   Update Containers:

#   Updated base config.yml file:
#     ansible-playbook plex-meta-manager.yaml --start-at-task="plex_meta_manager : write_plex_meta_manager_config_from_template"

#   Updated library yml files:
#     ansible-playbook plex-meta-manager.yaml --start-at-task="plex_meta_manager : copy_plex_meta_manager_configs"

#   Added new library and files:
#     ansible-playbook plex-meta-manager.yaml --start-at-task="plex_meta_manager : create_plex_meta_manager_config_directories"

# Manually run the container once
#   podman run --rm -it --name pmm -e TZ="America/Denver" -v "/mnt/nasa-p1z2-docker-data/plex-meta-manager/config:/config:rw" meisnate12/plex-meta-manager -r -w 200

- name: run_plex_meta_manager
  become: false
  gather_facts: true
  hosts: plex

  vars_prompt:
    # Trakt PIN is a one time use, Click Authorize under https://trakt.tv/oauth/applications/99201 if trakt is failing.
    # client id + secret + pin will fill out the authorization section on first run, access and refresh tokens be udpated.
    - name: "pmm.trakt.onetimeuse_pin"
      prompt: "trakt aut pin [https://trakt.tv/oauth/applications/99201]"
      private: no

  tasks:
    - name: run_plex_meta_manager_container
      import_role:
        name: plex_meta_manager
