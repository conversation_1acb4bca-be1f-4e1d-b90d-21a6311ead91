---
#   Plex Install:
#     ansible-playbook -l plex jellyfin.yaml
#   Update Containers:
#     ansible-playbook -l plex jellyfin.yaml --start-at-task="jellyfin : start_jellyfin_container"

- name: Set up jellyfin server
  become: true
  gather_facts: true
  hosts: plex

  tasks:
    - name: setup_docker_service
      import_role:
        name: docker-host
    - name: setup_jellyfin_services
      import_role:
        name: jellyfin
