# Configure a raspberry pi SD card
# RUN:
#     ansible-playbook -v -kK -l pi-003 RaspberryPi/999_installRpiEeprom.yaml -u alarm
#
# Does not work currently use raspberry os to set eeprom settings.

---
- name: Setup the pi with netboot using Arch Linux ARM (alarm) for a raspberry pi.
  hosts: all

  vars:
    rpieep_dir: "/tmp/rpi-eeprom"

  tasks:
    - name:  Install dependencies git and base-devel
      block:
      - name: Install git
        become: yes
        pacman:
          name: git
          update_cache: yes
          state: present

      - name: Install base-devel
        become: yes
        pacman:
          name: base-devel
          update_cache: yes
          state: present

      - name: Let make sure we use more than one proc
        ansible.builtin.lineinfile:
          path: /etc/makepkg.conf
          regexp: '^MAKEFLAGS='
          line: 'MAKEFLAGS="j4"'
      become: true

    - name: Validating {{ rpieep_dir }} dir exists
      stat:
        path: "{{ rpieep_dir }}"
      register: rpieep_clone_dir

    - name: <PERSON>lone rpi-eeprom repo
      git:
        repo: https://aur.archlinux.org/rpi-eeprom.git
        dest: "{{ rpieep_dir }}"
      when: rpieep_clone_dir.stat.exists == false

    - name: Build rpi-eeprom
      command: makepkg -si --noconfirm
      args:
        chdir: "{{ rpieep_dir }}"

    # - name: See eeprom version
    #   command: rpi-eeprom-update
