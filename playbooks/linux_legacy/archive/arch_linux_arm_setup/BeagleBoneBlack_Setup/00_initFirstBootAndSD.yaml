# hosts
# 1) can be localhost with a connection type of local for the SD card
# 2) The live boot of the SD card after running the init
---
- name: Init for SD or flash with Arch Linux ARM (alarm) for the beaglebone black init setup.
  # Uncomment the following and comment the hosts bbb for creating an SD card.
  # hosts: localhost
  # connection: local
  # vars:
  #   drive_name: sdb
  #   partition_name: "1"
  hosts: bbb_live_stage1
  vars:
    drive_name: mmcblk1
    partition_name: p1
    poweroff_when_complete: true
  become: true
  # vars_prompt:
  #   - name: drive_name
  #     prompt: "Enter the dev name to format mmcblk# or sdX"
  #     private: no
  #   - name: partition_name
  #     prompt: "Enter the dev partition number p1 or 1"
  #     private: no

  tasks:
    - name: "Zero the device"
      command: "dd if=/dev/zero of=/dev/{{ drive_name }} bs=1M count=8"

    - name: "Partition /dev/{{ drive_name }}"
      raw: echo -en "o\np\nn\np\n1\n2048\n\n\np\nw\n" | fdisk /dev/{{ drive_name }}

    - name: "Create ext4 FS"
      command: "mkfs.ext4 /dev/{{ drive_name }}{{ partition_name }}"

    - name: Validating ./mnt dir exists
      stat:
        path: ./mnt
      register: mnt_dir

    - name: Create ./mnt dir
      file:
        path: ./mnt
        state: directory
      when: mnt_dir.stat.exists == false

    # We use the raw command as we don't want it in fstab.
    - name: "Mount the device partition to ./mnt"
      command: "mount /dev/{{ drive_name }}{{ partition_name }} ./mnt"
      # mount:
      #   path: "./mnt"
      #   src: /dev/{{ drive_name }}{{ partition_name }}
      #   fstype: ext4
      #   state: mounted


    - name: "Validating ./files dir exists"
      stat:
        path: "./files"
      register: files_dir

    - name: "Create ./files dir"
      file:
        path: "./files"
        state: directory
        mode: '0777'
      when: files_dir.stat.exists == false

    - name: "Copy the arch linux arm tar file from local"
      copy:
        src: "./files/ArchLinuxARM-am33x-latest.tar.gz"
        dest: "./files/ArchLinuxARM-am33x-latest.tar.gz"

    - name: "Extract the arm files"
      command: "bsdtar -xpf ./files/ArchLinuxARM-am33x-latest.tar.gz -C ./mnt"

    # Servers reliability seem to be an issue with downloading this file.
    # - name: Get the arch linux arm file
    #   command: "wget http://os.archlinuxarm.org/os/ArchLinuxARM-am33x-latest.tar.gz -P ./files"

    # This might not be extracting the files correctly.
    # - name: Extract arch image to directory.
    #   unarchive:
    #     src: http://os.archlinuxarm.org/os/ArchLinuxARM-am33x-latest.tar.gz
    #     dest: ./mnt
    #     remote_src: yes

    - name: "Sync the filesystem after extraction of tar"
      command: sync

    - name: "Install MLO bootloader"
      raw: "dd if=./mnt/boot/MLO of=/dev/{{ drive_name }} count=1 seek=1 conv=notrunc bs=128k"

    - name: "Install U-boot bootloader image"
      raw: "dd if=./mnt/boot/u-boot.img of=/dev/{{ drive_name }} count=1 seek=1 conv=notrunc bs=128k"

    - name: "Unmounting ./mnt"
      command: umount ./mnt
      # mount:
      #   path: "./mnt"
      #   state: unmounted

    - name: Sync the filesystem for final ejection
      command: sync

    - name: Shutdown the system, remove the SD and power it back up and run the init again.
      command: poweroff
      when: poweroff_when_complete
