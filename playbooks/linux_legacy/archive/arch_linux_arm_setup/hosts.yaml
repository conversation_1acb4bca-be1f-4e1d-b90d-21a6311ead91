# vim: set syntax=yaml:

---
all:
  hosts:
  children:
    linuxServers:
      children:
        debianServers:
        arch_arm:
    debianServers:
      vars:
        # Because ansible fails due to maintaining a list of distros and paths to python.
        ansible_python_interpreter: auto
      hosts:
        srv-001:
          ansible_host: *************
        bombadil:
          ansible_host: *************
        media-capture:
          ansible_host: *************
        bastion:
          ansible_host: *************
        burner:
          ansible_host: **************
    archLinuxPis:
      hosts:
        pi-001:
          ansible_host: *************
        pi-002:
          ansible_host: *************
        pi-003:
          ansible_host: *************
        pi-004:
          ansible_host: *************
    arcLinuxbbb:
      hosts:
        beagleBoneBlack:
          ansible_host: *************
    testServers:
      hosts:
        arch_arm_pi:
          ansible_host: **************
          user: alarm
          ansible_password: alarm
