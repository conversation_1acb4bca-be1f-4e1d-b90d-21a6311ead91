- name: Starting arch live base setup
  hosts: live_arch
  become: true
  tasks:
   - name: Setting NTP
     command: timedatectl set-ntp true
   #- name: "Updating pacman equivalent of 'pacman -Syu'"
   #  pacman:
   #    update_cache: yes
   #    upgrade: yes
   - name: install reflector
     pacman:
       name: reflector
       update_cache: yes
       state: present
   #    register: reflector_install
   - name: Setting mirrorlist with reflector
     command: reflector --country 'United States' --protocol http --latest 30 --number 20 --sort rate --save /etc/pacman.d/mirrorlist
   #  when: not reflector_install.failed

#  - name: enable nginx service
#    service:
#      name: nginx
#      enabled: yes
#
#  - name: ansible create directory example
#    file:
#      path: /etc/nginx/sites-enabled
#      state: directory
#
#  - name: copy nginx config file
#    copy:
#      src: ./files/nginx.conf
#      dest: /tmp/nginx.conf
#
#  - name: check nginx.conf exists
#    stat: path=/tmp/nginx.conf
#    register: conf_file
#
#  - name: moving nginx config file
#    command: mv /tmp/nginx.conf /etc/nginx/nginx.conf
#    when: conf_file.stat.exists
#
#  - name: copy index.html
#    template:
#      src: ./templates/index.html.j2
#      dest: /usr/share/nginx/html/index.html
#      mode: 0644
#
#  - name: restart nginx
#    service:
#      name: nginx
#      state: started
