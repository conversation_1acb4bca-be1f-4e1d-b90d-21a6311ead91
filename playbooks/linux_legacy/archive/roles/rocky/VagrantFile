# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
    config.vm.box = "rockylinux/8"
    config.vm.box_version = "4.0.0"

    config.ssh.insert_key = false

    config.vm.synced_folder ".", "/vagrant", disabled: true

    config.vm.provider :virtualbox do |v|
      v.memory = 256
      v.linked_clone = true
    end

    config.vm.define "host1" do |host|
      host.vm.hostname = "host1.test"
      host.vm.network :private_network, ip:"*************"
    end

    config.vm.define "host2" do |host|
      host.vm.hostname = "host2.test"
      host.vm.network :private_network, ip:"*************"
    end

    config.vm.define "host3" do |host|
      host.vm.hostname = "host2.test"
      host.vm.network :private_network, ip:"*************"
    end
  end
