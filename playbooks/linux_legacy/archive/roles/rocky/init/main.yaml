---

# Run:
#   CWD: ansible/playbooks/linux_plays/
#   ansible-playbook -l vagrantRockyServers -k -u vagrant roles/rocky/init/main.yaml
# or
#   ansible-playbook -l vagrantRockyServers -kK roles/rocky/init/main.yaml

# Installs and sets up sudo access as well as changing default root passwords

- name: Init a fresh Rocky linux install
  become: yes
  # gather_facts: False
  hosts:
  - all

  # vars_files:
  #   - vars.yaml
  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user_name: "krizzo"
    my_user_group: "krizzo"
    sudoers_path: "/etc/sudoers.d/"
    ssh_key_name: "id_ed25519"
    ssh_key_path: "/home/<USER>/.ssh"

  # This is not needed with rocky as we create our user on install
  # vars_prompt:
    # - name: "new_passwd"
    #   prompt: "New password"
    #   private: yes
    #   confirm: yes
    #   encrypt: sha512_crypt
    #   salt_size: 7
    # - name: "ssh_key_name"
    #   prompt: "SSH key filename [id_ed25519]"
    #   private: no

  tasks:
    - name: Debug username
      ansible.builtin.debug:
        msg: "Username: {{ my_user_name }}\nOS_Family: {{ ansible_facts['os_family']|lower }}"
        verbosity: 1

    - name: Update dnf cache if needed
      ansible.builtin.dnf:
        update_cache: yes

    - name: Import base pkgs
      import_tasks: ../tasks/base_pkgs.yaml

    - name: Set user {{ my_user_name }}
      import_tasks: ../tasks/set_user.yaml

    # - name: SE linux settings
    #   import_tasks: ../tasks/selinux.yaml
