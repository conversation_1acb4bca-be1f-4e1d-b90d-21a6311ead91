---
    - name: Install required packages for selinux
      ansible.builtin.dnf:
        pkg:
          - python39
          - python3-policycoreutils
          - python3-libselinux
        state: present
      when: ansible_facts['os_family']|lower == 'rocky'
              or ansible_facts['os_family']|lower == 'centos'
              or ansible_facts['os_family']|lower == 'redhat'

    - name: Put SELinux in permissive mode, logging actions that would be blocked.
      ansible.posix.selinux:
        policy: targeted
        state: permissive
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'
            or ansible_facts['os_family']|lower == 'redhat'

    # - name: Disable SELinux
    #   ansible.posix.selinux:
    #     state: disabled

    - name: Unconditionally reboot the machine with all defaults
      ansible.builtin.reboot:
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'
            or ansible_facts['os_family']|lower == 'redhat'
