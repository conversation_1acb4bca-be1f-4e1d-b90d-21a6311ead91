---

- name: Install the base list of packages
  ansible.builtin.dnf:
    pkg:
      - epel-release
      - vim
      - mtr
      - curl
      - zsh
      - tmux
      - jq
      - git
      - bind-utils
      - nfs-utils
      - dnf-utils
      - python39
    state: present
  when: ansible_facts['os_family']|lower == 'rocky'
          or ansible_facts['os_family']|lower == 'centos'
          or ansible_facts['os_family']|lower == 'redhat'
