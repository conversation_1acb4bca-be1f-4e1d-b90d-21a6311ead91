---

    - name: determine available groups
      ansible.builtin.getent:
        database: group

    - name: determine available users
      ansible.builtin.getent:
        database: passwd

    - name: create random number for group id
      ansible.builtin.set_fact:
        random_num: "{{ range(1500, 2000) | random(seed=item) }}"
      run_once: yes
      with_items:
        - string

    - name: "set group {{ my_user_name }} with gid {{ random_num }} when not available"
      ansible.builtin.group:
        name: "{{ my_user_name }}"
        gid: "{{ random_num }}"
      when:
        - "'{{ my_user_name }}' not in ansible_facts.getent_group"
        - "'{{ random_num }}' not in item.value"
      loop: "{{ ansible_facts.getent_group | dict2items }}"

    - name: "Getting sudoers file status for {{ my_user_name }}"
      ansible.builtin.stat:
        path: "{{ sudoers_path }}{{ my_user_name }}"
      register: sudoers_file

    - name: "Setting {{ my_user_name }} sudoers file if doesn't exist"
      ansible.builtin.lineinfile:
        dest: "{{ sudoers_path }}{{ my_user_name }}"
        line: '{{ my_user_name }} ALL=(ALL) NOPASSWD: ALL'
        create: yes
        owner: root
        group: root
        mode: "0440"
        state: present
        validate: 'visudo -c -f %s'
      when: sudoers_file.stat.exists == false

    - name: "Create .ssh dir for {{ my_user_name }}"
      ansible.builtin.file:
        path: "{{ ssh_key_path }}"
        state: directory
        mode: '0700'
        owner: "{{ my_user_name }}"
        group: "{{ my_user_name }}"

    - name: Add SSH public key for {{my_user_name}} user
      ansible.posix.authorized_key:
        user: "{{my_user_name}}"
        key: "{{ lookup('file', '~/.ssh/id_ed25519.pub') }}"
        state: present

    # - name: Debug ssh public key file
    #   ansible.builtin.debug:
    #     msg: "ssh_pub_key_path: {{ lookup('env','HOME') + '/.ssh/id_ed25519.pub' }}"
    #     verbosity: 1

    # - name: Getting ssh key file status
    #   delegate_to: localhost
    #   become: false
    #   ansible.builtin.stat:
    #     path: "{{ lookup('env','HOME') + '/.ssh/id_ed25519.pub' }}"
    #     # path: "{{ lookup('env','HOME') + '/.ssh/' + ssh_key_name + '.pub' }}"
    #   register: ssh_key_file_check
    #   when: ssh_dir.stat.exists == true

    # # Requirements: ansible-galaxy collection install community.crypto
    # - name: "Generate ssh key pair for {{ my_user_name }}"
    #   community.crypto.openssh_keypair:
    #     path: "{{ lookup('env','HOME') + '/.ssh/' + ssh_key_name }}"
    #     type: "{{ ssh_key_type }}"
    #     owner: "{{ my_user_name }}"
    #     group: "{{ my_user_name }}"
    #     # ssh_key_passphrase: "{{ new_passwd }}"
    #     ssh_key_comment: "{{ ssh_key_comment }}"
    #   delegate_to: localhost
    #   when: ssh_key_file_check.stat.exists == false

    # - name:  "Set authorized key taken from file for user {{ my_user_name }}"
    #   become: false
    #   ansible.posix.authorized_key:
    #     user: "{{ lookup('env', 'USER') }}"
    #     state: present
    #     key: "{{ lookup('file', lookup('env','HOME') + '/.ssh/id_ed25519.pub' ) }}"
    #     # key: "{{ lookup('file', lookup('env','HOME') + '/.ssh/' + ssh_key_name + '.pub' ) }}"
    #   when: ssh_key_file_check.stat.exists == true
