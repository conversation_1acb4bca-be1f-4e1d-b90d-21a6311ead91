---
- name: <PERSON><PERSON> <PERSON>
  import_tasks: ../tasks/pkgs.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
        or ansible_facts['os_family']|lower == 'centos'

- name: Install list of packages for Docker
  ansible.builtin.yum:
    pkg:
      - dnf-utils
      # - apt-transport-https
      # - ca-certificates
      # - curl
      # - gnupg2
      # - software-properties-common
    state: present
  when: ansible_facts['os_family']|lower == 'rocky'
      or ansible_facts['os_family']|lower == 'centos'

- name: "Configuring docker-ce repo"
  ansible.builtin.get_url:
    url: https://download.docker.com/linux/centos/docker-ce.repo
    dest: /etc/yum.repos.d/docker-ce.repo
    mode: 0644
  when: ansible_facts['os_family']|lower == 'rocky'
      or ansible_facts['os_family']|lower == 'centos'

# - name: Add the docker CE repo
#   ansible.builtin.yum_repository:
#     name: Docker CE Stable
#     description: Docker CE Stable Repo
#     baseurl: https://download.docker.com/linux/centos/$releasever/$basearch/stable
#     gpgkey: https://download.docker.com/linux/centos/gpg
#     gpgcheck: yes
#     enablerepo: yes

- name: Install docker-ce
  ansible.builtin.yum:
    pkg:
    - docker-ce
    - docker-ce-cli
    update_cache: yes
    state: latest

- name: Adding existing user krizzo to group docker
  ansible.builtin.user:
    name: krizzo
    groups: docker
    append: yes

- name: Starting and Enabling Docker service
  ansible.builtin.systemd:
    name: docker
    state: started
    enabled: yes

# - name: Install python docker module
#   ansible.builtin.pip:
#     name:
#       - requests
#       - docker
#     executable: pip3
