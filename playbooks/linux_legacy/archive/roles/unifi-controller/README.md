# Unifi AP Informationmn

## Factory Reset the AP

1. Press and hold the reset button for 10 seconds while AP is connected.
2. Release the button (the LEDs on the UAP will stop glowing).
3. Do not disconnect the UAP from its power source during the reboot process.
4. The UAP will restore factory settings

Default username/password: ubnt/ubnt

## Adopt the AP to a controller

https://help.ui.com/hc/en-us/articles/360012622613-UniFi-Device-adoption

### Using the controller

This is the best and easiest method but requires you make a change on the controller first.

The webui is at https://ip:8443, setup with the first run wizard.

For Unifi to adopt other devices, e.g. an Access Point, it is __required__ to change the inform ip address. Because Unifi runs inside Docker by default it uses an ip address not accessable by other devices. To change this go to Settings > Controller > Controller Settings and set the Controller Hostname/IP to an ip address accessable by other devices.

### SSH to the AP

```bash
ssh ubnt@*************
sudo syswrapper.sh restore-default
set-inform http://*************:8080/inform
set-inform
```

## AP lights

|Device Activity|LED Pattern|
| :-: | :-: |
| __Initializing__ <br> AP is Booting | ![Flashing White / Off Every 1/2s](files/led_images/UAP-AC-1-Initializing.gif) <br> Flashing White / Off Every 1/2s|
| __Factory Defaults__ <br> AP is Awaiting Adoption | ![Steady White](files/led_images/UAP-AC-2-Factory-Defaults.gif) <br> Steady White|
| __BLE Connection (UDM Only)__ <br> A client is connected to the UDM via Bluetooth (BLE) | ![Slow Flashing Blue](files/led_images/UDM-BLE.gif) Slow Flashing Blue |
| __Adopted__ <br> AP is Broadcasting SSIDs; Normal Operating Mode | ![Steady Blue](files/led_images/UAP-AC-4-Adopted.gif) Steady Blue |
| __Initialization Error A12__ <br> Power Cycle AP and Contact Support if Recurring | ![Strobing White / Off](files/led_images/UAP-AC-9-Error-A12.gif) Strobing White / Off |
| __Firmware Upgrade__ <br> AP is Busy with Upgrade. Do not Interrupt! <br> In the UDM's case, it will flash only white during upgrade. | ![Quickly Flashing White / Blue](files/led_images/UAP-AC-7-Firmware-Upgrade.gif) Quickly Flashing White / Blue|
| __Isolated__ <br> AP Lost Network Connectivity, Searching for Wireless Uplink | ![Blue and Flashing Off Every 5s](files/led_images/UAP-AC-5-Isolated.gif) Blue and Flashing Off Every 5s |
| __Locating__ <br> The AP Locate Feature was Activated in Controller or the UniFi AC EasySetup App | ![Rapid Flashing Blue / Off](files/led_images/UAP-AC-6-Locating.gif) Rapid Flashing Blue / Off |
| __TFTP Mode__ <br> The device is in TFTP mode, which is triggered by holding the reset button before applying power, then continuing to hold reset until this White/Blue/Off/repeat sequence is seen. If this is not intentional, check and see if the reset button is jammed, it should click when pushed. | ![Flashing White-Blue-Off](files/led_images/UAP-AC-TFTP.gif) Flashing White-Blue-Off |
| __Device Offline__ <br> Verify Power, POE and Ethernet Cable | ![LED Off](files/led_images/UAP-AC-8-LED-Off.gif) LED Off |
