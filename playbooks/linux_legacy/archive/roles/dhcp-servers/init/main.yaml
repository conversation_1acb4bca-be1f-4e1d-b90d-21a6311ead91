---

# Requirements: ansible galaxy posix
# ansible-galaxy collection install ansible.posix

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base Insatll:
#     ansible-playbook -l dhcpServers -kK roles/rocky/init/main.yaml
#   DHCP Install:
#     ansible-playbook -l dhcpServers roles/dhcp-servers/init/main.yaml
#   UPDATE conf files:
#     ansible-playbook -l dhcpServers roles/dhcp-servers/init/main.yaml --start-at-task="Copy shared conf files"


- name: DHCP Updates
  become: true
  hosts:
  # - dhcpVagrants
  - dhcpServers

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"

    dhcp_confg_src: "../files/dhcp/"
    dhcpd_v4_ints_list:
      - "eth0"
      # - "br0"
    primary_dhcpd_conf: "dhcpd_primary.conf"
    secondary_dhcpd_conf: "dhcpd_secondary.conf"

    dhcpd_conf_dst: "/etc/dhcp/"
    dhcpd_conf_filename: "dhcpd.conf"
    dhcpd_owner: "dhcpd"
    dhcpd_group: "dhcpd"
    dhcpd_service_file: "/etc/systemd/system/dhcpd.service"

  handlers:
    - name: Restart dhcpd
      ansible.builtin.systemd:
        name: dhcpd
        state: restarted

    - name: Reload firewalld
      ansible.builtin.systemd:
        name: firewalld
        state: reloaded

  tasks:

    # TODO: Use ansible to generate an OMAPI key and modify the dhcpd.conf files.
    # TODO: Split the vagrant and true DHCP files out with specific tasks based on vagrant host or real hosts

    # - name: Update yum cache if needed
    #   ansible.builtin.yum:
    #     update_cache: yes

    - name: Import base pkgs
      import_tasks: ../tasks/base_pkgs.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Ensure dhcp-server present
      ansible.builtin.yum:
        name: dhcp-server
        state: present

    - name: Allow ports in firewalld
      ansible.posix.firewalld:
        port: "{{ item.port }}"
        permanent: yes
        state: enabled
      loop:
        - port: 67-68/udp # BOOTPS/BOOTPC
        - port: 647/tcp # DHCP Failover Primary TCP
        - port: 847/tcp # DHCP Failover Secondary TCP
        - port: 7911/tcp # OMAPI control port TCP
      notify: Reload firewalld

    # - name: Allow BOOTPS and BOOTPC ports in IPtables
    #   ansible.builtin.iptables:
    #       chain: INPUT
    #       protocol: "{{ item.protocol }}"
    #       source_port: "{{ item.port }}"
    #       destination_port: "{{ item.port }}"
    #       jump: ACCEPT
    #       comment: "ALLOW {{ item.comment }}"
    #   loop:
    #     - port: 67
    #       protocol: "udp"
    #       comment: "BOOTPS UDP"
    #     - port: 68
    #       protocol: "udp"
    #       comment: "BOOTPC UDP"
    #     - port: 647
    #       protocol: "tcp"
    #       comment: "DHCP Failover Primary TCP"
    #     - port: 847
    #       protocol: "tcp"
    #       comment: "DHCP Failover Secondary TCP"
    #     - port: 7911
    #       protocol: "tcp"
    #       comment: "OMAPI control port TCP"

    - name: Create the dhcpd directories
      ansible.builtin.file:
        path: "{{ dhcpd_conf_dst }}{{item.path}}"
        state: directory
        mode:  '0755'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      with_filetree: "{{ dhcp_confg_src }}"
      when:
        - item.state == 'directory'
        # - item.path is not match("vagrant*")

    - name: Copy shared conf files
      ansible.builtin.copy:
        src: "{{item.src}}"
        dest: "{{ dhcpd_conf_dst }}{{item.path}}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      with_filetree: "{{ dhcp_confg_src }}"
      when:
        - item.state == 'file'
        - item.path is not search("\.keep*")
        - item.path is not search(".*dhcpd_.*")
            # Avoid copying the dhcpd.conf it will be different between the two hosts for HA
            # See https://docs.ansible.com/ansible/latest/user_guide/playbooks_tests.html for more on search and match
      notify: Restart dhcpd

    - name: Cope primary dhcpd conf file
      ansible.builtin.copy:
        src: "{{ dhcp_confg_src }}{{ primary_dhcpd_conf }}"
        dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      when: (ansible_facts['hostname']|lower == "silmarillion-001")
      notify: Restart dhcpd

    - name: Cope secondary dhcpd conf file
      ansible.builtin.copy:
        src: "{{ dhcp_confg_src }}{{ secondary_dhcpd_conf }}"
        dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      when: (ansible_facts['hostname']|lower == "silmarillion-002")
      notify: Restart dhcpd

    # - name: Set listening interfaces
    #   ansible.builtin.copy:
    #     remote_src: yes
    #     src: "/usr/lib/systemd/system/dhcpd.service"
    #     dest: "{{ dhcpd_service_file }}"
    #     mode:  '0755'
    #   notify: Restart dhcpd

    # # Space separated interface name list
    # - name: Concatenate the ints list for DHCP config v4
    #   set_fact:
    #     dhcpd_v4_ints_str: "{{ dhcpd_v4_ints_list | join(' ') }}"

    # - name: Set v4 listening interface(s)
    #   ansible.builtin.replace:
    #     path: "{{ dhcpd_service_file }}"
    #     regexp: '(^ExecStart.*DHCPDARGS)'
    #     replace: '\1 {{ dhcpd_v4_ints_str }}'
      # notify: Restart dhcpd

    - name: Starting dhcpd service
      ansible.builtin.service:
        name: dhcpd
        enabled: true
        state: started
