```log
Working results from dhcpd service status

# systemctl status dhcpd

Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: Server starting service.
Aug 31 18:03:21 dhcpSrv2 systemd[1]: Started DHCPv4 Server Daemon.
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: peer dhcp-failover: disconnected
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: failover peer dhcp-failover: I move from startup to communications-interrupted
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: failover peer dhcp-failover: peer moves from normal to communications-interrupted
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: failover peer dhcp-failover: I move from communications-interrupted to normal
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: balancing pool 55ada64b12f0 192.168.128.0/24  total 191  free 96  backup 95  lts 0  max-own (+/-)19
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: balanced pool 55ada64b12f0 192.168.128.0/24  total 191  free 96  backup 95  lts 0  max-misbal 29
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: failover peer dhcp-failover: peer moves from communications-interrupted to normal
Aug 31 18:03:21 dhcpSrv2 dhcpd[5036]: failover peer dhcp-failover: Both servers normal

# tail -f /var/lib/dhcpd/dhcpd.leases
  my state normal at 2 2021/08/31 18:03:21;
  partner state communications-interrupted at 2 2021/08/31 18:03:21;
  mclt 3600;
}
failover peer "dhcp-failover" state {
  my state normal at 2 2021/08/31 18:03:21;
  partner state normal at 2 2021/08/31 18:03:21;
  mclt 3600;
}
lease ************** {
  starts 2 2021/08/31 18:14:49;
  ends 2 2021/08/31 19:14:49;
  tstp 2 2021/08/31 19:44:49;
  tsfp 2 2021/08/31 18:03:18;
  cltt 2 2021/08/31 18:14:49;
  binding state active;
  next binding state expired;
  hardware ethernet 08:00:27:07:75:a4;
  uid "\001\010\000'\007u\244";
  client-hostname "host1";
}
lease ************** {
  starts 2 2021/08/31 18:14:49;
  ends 2 2021/08/31 19:14:49;
  tstp 2 2021/08/31 19:44:49;
  tsfp 2 2021/08/31 19:44:49;
  atsfp 2 2021/08/31 19:44:49;
  cltt 2 2021/08/31 18:14:49;
  binding state active;
  next binding state expired;
  hardware ethernet 08:00:27:07:75:a4;
  uid "\001\010\000'\007u\244";
  client-hostname "host1";
}
```
