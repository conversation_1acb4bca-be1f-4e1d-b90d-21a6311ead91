---

    # TODO: Use ansible to generate an OMAPI key and modify the dhcpd.conf files.
    # TODO: Split the vagrant and true DHCP files out with specific tasks based on vagrant host or real hosts

    - name: Update yum cache if needed
      ansible.builtin.yum:
        update_cache: yes

    - name: Ensure dhcp-server present
      ansible.builtin.yum:
        name: dhcp-server
        state: present

    - name: Allow BOOTPS and BOOTPC ports in IPtables
      ansible.builtin.iptables:
          chain: INPUT
          protocol: "{{ item.protocol }}"
          source_port: "{{ item.port }}"
          destination_port: "{{ item.port }}"
          jump: ACCEPT
          comment: "ALLOW {{ item.comment }}"
      loop:
        - port: 67
          protocol: "udp"
          comment: "BOOTPS UDP"
        - port: 68
          protocol: "udp"
          comment: "BOOTPC UDP"
        - port: 647
          protocol: "tcp"
          comment: "DHCP Failover Primary TCP"
        - port: 847
          protocol: "tcp"
          comment: "DHCP Failover Secondary TCP"
        - port: 7911
          protocol: "tcp"
          comment: "OMAPI control port TCP"

    - name: Create the dhcpd directories
      ansible.builtin.file:
        path: "{{ dhcpd_conf_dst }}{{item.path}}"
        state: directory
        mode:  '0755'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      with_filetree: "{{ dhcp_confg_src }}"
      when:
        - item.state == 'directory'
        - item.path is not match("vagrant*")

    - name: Copy shared conf files
      ansible.builtin.copy:
        src: "{{item.src}}"
        dest: "{{ dhcpd_conf_dst }}{{item.path}}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      with_filetree: "{{ dhcp_confg_src }}"
      when:
        - item.state == 'file'
        - item.path is not match("dhcpd_*")
            # Avoid copying the dhcpd.conf it will be different between the two hosts for HA
      notify: Restart dhcpd

    - name: Cope primary dhcpd conf file
      ansible.builtin.copy:
        src: "{{ dhcp_confg_src }}{{ primary_dhcpd_conf }}"
        dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      when: (ansible_facts['hostname']|lower == "silmarillion-001")
      notify: Restart dhcpd

    - name: Cope secondary dhcpd conf file
      ansible.builtin.copy:
        src: "{{ dhcp_confg_src }}{{ secondary_dhcpd_conf }}"
        dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
        mode:  '0644'
        owner: "{{ dhcpd_owner }}"
        group: "{{ dhcpd_group }}"
      when: (ansible_facts['hostname']|lower == "silmarillion-001")
      notify: Restart dhcpd

    - name: Set listening interfaces
      ansible.builtin.copy:
        remote_src: yes
        src: "/usr/lib/systemd/system/dhcpd.service"
        dest: "{{ dhcpd_service_file }}"
        mode:  '0755'
      notify: Restart dhcpd

    # Space separated interface name list
    - name: Concatenate the ints list for DHCP config v4
      set_fact:
        dhcpd_v4_ints_str: "{{ dhcpd_v4_ints_list | join(' ') }}"

    - name: Set v4 listening interface(s)
      ansible.builtin.replace:
        path: "{{ dhcpd_service_file }}"
        regexp: '(^ExecStart.*DHCPDARGS)'
        replace: '\1 {{ dhcpd_v4_ints_str }}'
      notify: Restart dhcpd

    - name: Starting dhcpd service
      ansible.builtin.service:
        name: dhcpd
        enabled: true
        state: started
