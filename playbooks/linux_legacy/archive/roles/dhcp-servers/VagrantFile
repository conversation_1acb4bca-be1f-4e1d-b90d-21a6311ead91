# -*- mode: ruby -*-
# vi: set ft=ruby :

# vagrant up
# for i in {1..4}; do vagrant halt host${i} ; done
# VirtualBox GUI
#   1) Add second adapter to each host as host-only
#   2) Start the host headless

Vagrant.configure("2") do |config|
    config.vm.box = "rockylinux/8"
    config.vm.box_version = "4.0.0"

    config.ssh.insert_key = false

    config.vm.synced_folder ".", "/vagrant", disabled: true

    config.vm.provider :virtualbox do |v|
        v.memory = 256
        v.linked_clone = true
    end

    config.vm.define "primary" do |host|
        host.vm.hostname = "dhcpSrv1"
        host.vm.network "private_network", ip: "*************", name: "vboxnet0"
    end

    config.vm.define "secondary" do |host|
        host.vm.hostname = "dhcpSrv2"
        host.vm.network "private_network", ip: "*************", name: "vboxnet0"
    end

    config.vm.define "host1" do |host|
        host.vm.hostname = "host1"
        # host.vm.network "private_network", type: "dhcp", name: "vboxnet0", auto_config: false
        # host.vm.network "private_network", type: "dhcp", name: "vboxnet0", :adapter => 2, auto_config: false
    end

    config.vm.define "host2" do |host|
        host.vm.hostname = "host2"
        # host.vm.network "private_network", type: "dhcp", name: "vboxnet0", :adapter => 2, auto_config: false, :mac => "aabbcc112233"
    end

    config.vm.define "host3" do |host|
        host.vm.hostname = "host3"
        # host.vm.network "private_network", type: "dhcp", name: "vboxnet0", :adapter => 2, auto_config: false
    end

    config.vm.define "host4" do |host|
        host.vm.hostname = "host4"
        # host.vm.network "private_network", type: "dhcp", name: "vboxnet0", :adapter => 2, auto_config: false
    end
  end

  # sudo tcpdump -i eth1 -v -s 0 port bootps or bootpc
