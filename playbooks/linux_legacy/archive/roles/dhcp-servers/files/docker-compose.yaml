---
version: "3.8"
services:
  dhcp-primary:
    image: ghcr.io/linuxserver/plex
    container_name: plex
    restart: unless-stopped
    network_mode: "host" # Ran into provisioning error with the other way.
    environment:
      - "PUID=1000"
      - "PGID=1000"
      - "VERSION=latest"
    volumes:
      - type: volume
        source: plex-movies
        target: /data/movies
        volume:
          nocopy: true
      - type: volume
        source: plex-series
        target: /data/series
        volume:
          nocopy: true
      - type: volume
        source: plex-music
        target: /data/music
        volume:
          nocopy: true
      - type: volume
        source: plex-config
        target: /config
        volume:
          nocopy: true
    ports:
      - "32400:32400"
      - "32400:32400/udp"
      - "32469:32469"
      - "32469:32469/udp"
      - "5353:5353/udp"
      - "1900:1900/udp"
    # devices:
    #   - "/dev/dvb"
        # Capture device

# Need to create the directory on the NFS server such as /plex/config before running
# docker-compose up -d
volumes:
  plex-movies:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/movies"
  plex-series:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/series"
  plex-music:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/music"
  plex-config:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/docker-data/plex/config"
  tautulli-config:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/docker-data/tautulli/config"
  tautulli-plex-logs:
    driver_opts:
      type: "nfs"
      o: "addr=192.168.28.31,nolock,soft,ro"
      device: ":/mnt/nasa-p1z2/docker-data/plex/config/Library/Application Support/Plex Media Server/Logs"
