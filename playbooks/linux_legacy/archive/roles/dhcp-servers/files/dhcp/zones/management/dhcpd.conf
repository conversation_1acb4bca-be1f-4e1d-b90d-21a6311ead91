subnet 192.168.25.0 netmask 255.255.255.0 {
    option subnet-mask 255.255.255.0;
    option routers 192.168.25.1;
    option domain-name-servers 1.1.1.1, 8.8.8.8;
    default-lease-time 3600; # 1 hour
    max-lease-time 3600; # 1 hour
    pool {
        failover peer "dhcp-failover";
        range 192.168.25.200 192.168.25.254;
        default-lease-time 3600; # 1 hour
        max-lease-time 3600; # 1 hour
    }
    # Set a bootstrap file based on it being MACADDR.cfg
    # See https://kb.isc.org/docs/aa-01039 for why each MAC section needs to be procssed due to leading 0's
    option bootfile-name = concat (
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,1,1))),2), ":",
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,2,1))),2), ":",
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,3,1))),2), ":",
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,4,1))),2), ":",
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,5,1))),2), ":",
    suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,6,1))),2)
    );
}
