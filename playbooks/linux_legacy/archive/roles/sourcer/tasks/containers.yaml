---

- name: Start deluge container
  community.docker.docker_container:
    name: deluge
    image: lscr.io/linuxserver/deluge
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - "8112:8112"
      - "6881:6881"
      - "6881:6881/udp"
    volumes:
      - /mnt/nasa-p1z2-docker-data-deluge-config:/config
      - /mnt/nasa-p1z2-media-movies:/data/movies
      - /mnt/nasa-p1z2-media-series:/data/series
      - /mnt/nasa-p1z2-media-music:/data/music
      - /downloads:/downloads # Local downloads
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"

- name: Start medusa container
  community.docker.docker_container:
    name: medusa
    image: lscr.io/linuxserver/medusa
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - "8081:8081"
    volumes:
      - /mnt/nasa-p1z2-docker-data-medusa-config:/config
      - /downloads:/downloads # Local downloads
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
