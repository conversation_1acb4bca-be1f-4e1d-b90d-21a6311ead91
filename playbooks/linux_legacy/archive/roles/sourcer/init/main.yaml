---

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base insatll:
#     ansible-playbook -l pippin -kK roles/rocky/init/main.yaml
#   Setup Install:
#     ansible-playbook -l pippin roles/sourcer/init/main.yaml
#   Update Containers:
#     ansible-playbook -l pippin roles/sourcer/init/main.yaml --start-at-task="Start containers"

- name: Set up sourcer server
  become: true
  gather_facts: true
  hosts: pippin

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user_name: "krizzo"
    my_user_group: "krizzo"

  tasks:
    - name: Import base pkgs
      import_tasks: ../tasks/pkgs.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Disable IPv6
      import_tasks: ../tasks/disable_ipv6.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Create NFS Mounts
      import_tasks: ../tasks/nfs_mounts.yaml

    - name: Install docker services
      import_tasks: ../tasks/docker_setup.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Firewalld Rules
      ansible.posix.firewalld:
        port: "{{ item.port }} "
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - port: 8112/tcp
        - port: 8081/tcp
        - port: 6881/tcp
        - port: 6881/udp

    - name: StartContainers
      import_tasks: ../tasks/containers.yaml
