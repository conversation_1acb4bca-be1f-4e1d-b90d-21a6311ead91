---
version: "3.8"
services:
  deluge:
    image: ghcr.io/linuxserver/deluge
    container_name: deluge
    restart: unless-stopped
    environment:
      - "PUID=1000"
      - "PGID=1000"
      - "TZ=America/Denver"
    volumes:
      - type: volume
        source: deluge-movies
        target: /data/movies
        volume:
          nocopy: true
      - type: volume
        source: deluge-series
        target: /data/series
        volume:
          nocopy: true
      - type: volume
        source: deluge-music
        target: /data/music
        volume:
          nocopy: true
      - type: volume
        source: deluge-config
        target: /config
        volume:
          nocopy: true
      # Only do this if pippin is on the same network as the NAS and not routed.
      # - type: volume
      #   source: deluge-downloads
      #   target: /downloads
      #   volume:
      #     nocopy: true
      - /downloads:/downloads
    ports:
      - 8112:8112
      - 6881:6881
      - 6881:6881/udp

# Need to create the directory on the NFS server such as /plex/config before running
# docker-compose up -d
volumes:
  deluge-movies:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/movies"
  deluge-series:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/series"
  deluge-music:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/Media/music"
  deluge-config:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/nasa-p1z2/docker-data/deluge/config"
  # Only do this if pippin is on the same network as the NAS and not routed.
  # deluge-downloads:
  #   driver_opts:
  #     type: "nfs"
  #     o: "addr=*************,nolock,soft,rw"
  #     device: ":/mnt/nasa-p1z2/temp-downloads"
