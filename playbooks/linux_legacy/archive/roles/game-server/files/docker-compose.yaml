---
version: '3'

services:
  mc-001:
    image: itzg/minecraft-server
    container_name: mc-001
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    volumes:
      - type: volume
        source: mc-001-data
        target: /data
        volume:
          nocopy: true

  mc-002:
    image: itzg/minecraft-server
    container_name: mc-002
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    volumes:
      - type: volume
        source: mc-002-data
        target: /data
        volume:
          nocopy: true

  mc-003:
    image: itzg/minecraft-server
    container_name: mc-003
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    volumes:
      - type: volume
        source: mc-003-data
        target: /data
        volume:
          nocopy: true

  mc-004:
    image: itzg/minecraft-server
    container_name: mc-004
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    volumes:
      - type: volume
        source: mc-004-data
        target: /data
        volume:
          nocopy: true

  factorio-001:
    image: goofball222/factorio
    container_name: factorio-001
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    network_mode: "host" # Required for auto discover on LAN
    ports:
      - "27015:27015"
      - "34197:34197/udp"
    environment:
      TZ: "UTC"
      PGID: 1000
      PUID: 1000
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - type: volume
        source: factorio-001-data
        target: /factorio
        volume:
          nocopy: true

  factorio-002:
    image: goofball222/factorio
    container_name: factorio-002
    restart: unless-stopped
    tty: true         # docker run -t
    stdin_open: true  # docker run -i
    network_mode: "host" # Required for auto discover on LAN
    ports:
      - "27015:27015"
      - "34197:34197/udp"
    environment:
      TZ: "UTC"
      PGID: 1000
      PUID: 1000
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - type: volume
        source: factorio-002-data
        target: /factorio
        volume:
          nocopy: true

# Need to create the directory on the NFS server such as mc-001 before running
# docker-compose up -d
volumes:
  mc-001-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/mc-001"

  mc-002-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/mc-002"

  mc-003-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/mc-003"

  mc-004-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/mc-004"

  factorio-001-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/factorio-001"

  factorio-002-data:
    driver_opts:
      type: "nfs"
      o: "addr=*************,nolock,soft,rw"
      device: ":/mnt/tank2/game-data/factorio-002"
