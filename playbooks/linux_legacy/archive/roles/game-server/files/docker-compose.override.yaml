# Minecraft Docker Container https://github.com/itzg/docker-minecraft-server/blob/master/README.md
# Minecraft Server Settings

# Set the time to day and stop time so it doesn't change
# /time set day
# /gamerule doDaylightCycle false
# /gamerule keepInventory true


version: '3'

x-mc-vars: &mc-vars
  ALLOW_FLIGHT: "true"
  ALLOW_NETHER: "true"
  ENABLE_RCON: "true"
  ENABLE_ROLLING_LOGS: "true"
  EULA: "TRUE"
  GENERATE_STRUCTURES: "true"
  MAX_BUILD_HEIGHT: "256"
  MEMORY: "4G"
  PLAYER_IDLE_TIMEOUT: "10"
  PVP: "false"
  OPS: "krizzo"
  RCON_PASSWORD: "RCON_PASSWD_CHANGE_ME"
  SPAWN_PROTECTION: "32"
  TZ: "UTC"
  SERVER_NAME: "mc-001"
  MOTD: "MC-001 Normal Server"

x-mc-vars: &mc-vars-flat-world
  MODE: "creative"
  LEVEL_TYPE: "flat"
  SPAWN_MONSTERS: "false"
  SERVER_NAME: "mc-002"
  MOTD: "MC-002 Flat World Server"

# https://aikar.co/2018/07/02/tuning-the-jvm-g1gc-garbage-collector-flags-for-minecraft/
x-mc-mod-vars: &mc-mod-vars
  TYPE: "FORGE"
  VERSION: "1.16.5"
  FORGEVERSION: "36.1.0"
  LEVEL_TYPE: "biomesoplenty"
  SERVER_NAME: "mc-003"
  MOTD: "MC-003 Mod Server"
  MEMORY: "10G"
  # JVM_OPTS: >-
  #   -Xms10G
  #   -Xmx10G
  JVM_XX_OPTS: >-
    -XX:+UseG1GC
    -XX:+ParallelRefProcEnabled
    -XX:MaxGCPauseMillis=200
    -XX:+UnlockExperimentalVMOptions
    -XX:+DisableExplicitGC
    -XX:+AlwaysPreTouch
    -XX:G1NewSizePercent=30
    -XX:G1MaxNewSizePercent=40
    -XX:G1HeapRegionSize=8M
    -XX:G1ReservePercent=20
    -XX:G1HeapWastePercent=5
    -XX:G1MixedGCCountTarget=4
    -XX:InitiatingHeapOccupancyPercent=15
    -XX:G1MixedGCLiveThresholdPercent=90
    -XX:G1RSetUpdatingPauseTimePercent=5
    -XX:SurvivorRatio=32
    -XX:+PerfDisableSharedMem
    -XX:MaxTenuringThreshold=1
  JVM_DD_OPTS: >-
    -Dusing.aikars.flags=https://mcflags.emc.gs
    -Daikars.new.flags=true

x-mc-mod-vars: &mc-mod-vars-flat-world
  LEVEL_TYPE: "flat"
  SPAWN_MONSTERS: "false"
  MODE: "creative"
  # GENERATOR_SETTINGS: "minecraft:bedrock,3*minecraft:stone,40*minecraft:dirt,1*minecraft:grass_block;minecraft:plains;"
  SERVER_NAME: "mc-004"
  MOTD: "MC-004 Mod Server Flat World"
  VERSION: "1.16.5"
  TYPE: "FORGE"
  FORGEVERSION: "36.2.0"
  MEMORY: "10G"
  # JVM_OPTS: >-
  #   -Xms10G
  #   -Xmx10G
  JVM_XX_OPTS: >-
    -XX:+UseG1GC
    -XX:+ParallelRefProcEnabled
    -XX:MaxGCPauseMillis=200
    -XX:+UnlockExperimentalVMOptions
    -XX:+DisableExplicitGC
    -XX:+AlwaysPreTouch
    -XX:G1NewSizePercent=30
    -XX:G1MaxNewSizePercent=40
    -XX:G1HeapRegionSize=8M
    -XX:G1ReservePercent=20
    -XX:G1HeapWastePercent=5
    -XX:G1MixedGCCountTarget=4
    -XX:InitiatingHeapOccupancyPercent=15
    -XX:G1MixedGCLiveThresholdPercent=90
    -XX:G1RSetUpdatingPauseTimePercent=5
    -XX:SurvivorRatio=32
    -XX:+PerfDisableSharedMem
    -XX:MaxTenuringThreshold=1
  JVM_DD_OPTS: >-
    -Dusing.aikars.flags=https://mcflags.emc.gs
    -Daikars.new.flags=true


x-factorio-vars: &factorio-vars
  TOKEN: "8258474878b03b8a2123b42718c2d4"



services:
  mc-001:
    ports:
      - 25565:25565
    environment:
      <<: *mc-vars

  mc-002:
    ports:
      - 25566:25565
    environment:
      <<: *mc-vars
      <<: *mc-vars-flat-world

  mc-003:
    image: itzg/minecraft-server:java11
    ports:
      - 25567:25565
      - 8123:8123
    environment:
      <<: *mc-vars
      <<: *mc-mod-vars

  mc-004:
    image: itzg/minecraft-server:java11
    ports:
      - 25568:25565
      - 8124:8123
    environment:
      <<: *mc-vars
      <<: *mc-mod-vars-flat-world

  factorio-001:
    environment:
      <<: *factorio-vars
