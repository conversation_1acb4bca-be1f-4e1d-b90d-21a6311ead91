---

    # IPTABLES  -I INPUT -i $LAN_IFACE -p udp --dport 67:68 --sport 67:68 -j ACCEPT
    - name: ALLOW game server port in IPTABLES
      ansible.builtin.iptables:
          chain: INPUT
          protocol: "{{ item.proto }}"
          source_port: "{{ item.port }}"
          destination_port: "{{ item.port }}"
          jump: ACCEPT
          comment: "ALLOW {{ item.comment }}"
      loop:
        - port: 25565
          proto: "tcp"
          comment: " Minecraft port 25565"
        - port: 25566
          proto: "tcp"
          comment: " Minecraft port 25566"
        - port: 25567
          proto: "tcp"
          comment: " Minecraft port 25567"
        - port: 8123
          proto: "tcp"
          comment: " Minecraft port 8123"
        - port: 25568
          proto: "tcp"
          comment: " Minecraft port 25568"
        - port: 8124
          proto: "tcp"
          comment: " Minecraft port 8124"
        - port: 25575
          proto: "tcp"
          comment: " Minecraft RCON port 25575"
        - port: 34197
          proto: "udp"
          comment: " Factorio port 34197"
        - port: 27015
          proto: "tcp"
          comment: " Factorio RCON port 27015"
