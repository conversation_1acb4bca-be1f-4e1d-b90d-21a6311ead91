---

- name: Install list of packages for Docker
  ansible.builtin.apt:
    pkg:
    - apt-transport-https
    - ca-certificates
    - curl
    - gnupg2
    - software-properties-common
    state: present
    update_cache: yes

- name: Add Docker GPG apt key
  apt_key:
    url: https://download.docker.com/linux/debian/gpg
    state: present

- name: Get linux standard base name
  command: lsb_release -cs
  register: deb_release_name

# - name: deb release
#   debug:
#     msg: "{{deb_release_name}}"

- name: Add Docker repo
  apt_repository:
    repo: deb [arch=amd64] https://download.docker.com/linux/debian {{ deb_release_name['stdout'] }} stable
    state: present
  when: deb_release_name['stdout'] is defined

- name: Update apt and install docker-ce
  apt:
    update_cache: yes
    pkg:
    - docker-ce
    - docker-ce-cli
    - containerd.io
    state: latest

- name: Adding existing user krizzo to group docker
  user:
    name: krizzo
    groups: docker
    append: yes


## Docker Compose Install
- name: Check current docker-compose version
  command: docker-compose --version
  register: docker_compose_current_version
  changed_when: false
  failed_when: false

- name: Debug docker-compose version
  debug:
    msg: "{{ docker_compose_ver }} AND {{ docker_compose_current_version }}"
  when: docker_compose_current_version.stdout is defined

- name: Delete existing docker-compose version if it's older
  file:
    path: "{{ docker_compose_path }}"
    state: absent
  when: >
    docker_compose_current_version.stdout is defined
    and docker_compose_ver not in docker_compose_current_version.stdout

- name: Install Docker Compose (if configured).
  get_url:
    url: https://github.com/docker/compose/releases/download/{{ docker_compose_ver }}/docker-compose-Linux-x86_64
    dest: "{{ docker_compose_path }}"
    mode: 0755
