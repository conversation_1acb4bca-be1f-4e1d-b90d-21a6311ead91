---

# Run:
#   CWD: ansible/playbooks/linux_plays/
#   ansible-playbook -l newDebianServer roles/debian/init/main.yaml

- name: Base Debian Setup
  become: yes
  hosts:
  - all

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"

  tasks:
    # - name: Debug
    #   debug:
    #     msg: "Testing"
    - name: Updating the system
      import_tasks: ../tasks/update_install.yaml

    # - name: Media Capture Host
    #   import_tasks: media-capture.yaml
    #   when: ansible_facts['hostname']|lower == 'media-capture'

    # - name: Burner Host
    #   import_tasks: burner.yaml
    #   when: ansible_facts['hostname']|lower == 'burner'

    # - name: DHCP Host
    #   include_role:
    #     name: debian
    #   when: ansible_facts['hostname']|lower == 'srv-001'


# - name: Getting ssh dir status
#   stat:
#     path: "{{ ssh_key_path }}"
#   register: ssh_dir

# - name: Getting ssh key file status
#   stat:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#   register: sshkey_file
#   when: ssh_dir.stat.exists == true

# - name: Create .ssh dir for {{ my_user }}
#   file:
#     path: "{{ ssh_key_path }}"
#     state: directory
#     mode: '0700'
#     owner: "{{ my_user }}"
#     group: "{{ my_user }}"
#   when: ssh_dir.stat.exists == false

# - name: Generating SSH key for {{ my_user }}
#   community.crypto.openssh_keypair:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#     type: "{{ ssh_key_type }}"
#     ssh_key_passphrase: "{{ new_passwd }}"
#     ssh_key_comment: "{{ ssh_key_comment }}"
#   when: sshkey_file.stat.exists == false

# - name: Setting ssh dir and file permissions for {{ my_user }}
