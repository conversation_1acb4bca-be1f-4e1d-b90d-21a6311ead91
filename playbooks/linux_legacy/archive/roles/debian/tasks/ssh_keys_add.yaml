---

- name: "Ensure ssh key exists for {{ my_user }}"
  openssh_keypair:
    path: "{{ lookup('env','HOME') + '/.ssh/id_ed25519' }}"
    type: ed25519
    owner: "{{ my_user }}"
    group: "{{ my_user }}"
  delegate_to: localhost
  run_once: true

- name: "Copy {{ my_user }} user ssh keys"
  authorized_key:
    user: "{{ lookup('env', 'USER') }}"
    state: present
    key: "{{ lookup('file', lookup('env','HOME') + '/.ssh/id_ed25519.pub') }}"
