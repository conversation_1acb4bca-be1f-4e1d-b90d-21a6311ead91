- name: Ensure NFS common is installed for debian
  apt:
    name: nfs-common
    state: present
    update_cache: yes
  when: ansible_facts['os_family']|lower == 'debian'

- name: Ensure NFS common is installed for centos
  dnf:
    name: nfs-common
    state: present
    update_cache: yes
  when: ansible_facts['os_family']|lower == 'centos'

- name: Create mountable dir
  file:
    path: /mnt/media
    state: directory
    mode: '0755'
    owner: krizzo
    group: krizzo

- name: set mountpoints
  mount:
    path: /mnt/media
    src: "{{ nas_server_ip }}:/mnt/tank1"
    fstype: nfs
    opts: users,defaults
    dump: 0
    passno: 0
    state: mounted
