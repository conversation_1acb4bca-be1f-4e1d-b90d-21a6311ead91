---

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base insatll:
#     ansible-playbook -l silmarillion-001 -kK roles/rocky/init/main.yaml
#   home-assistant Install:
#     ansible-playbook -l silmarillion-001 roles/home-assistant/init/main.yaml

- name: Set up home-assistant server
  become: true
  gather_facts: true
  hosts: silmarillion-001

  # vars_files:
  #   - vars.yaml

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user_name: "krizzo"
    my_user_group: "krizzo"

  tasks:
    - name: Import base pkgs
      import_tasks: ../tasks/pkgs.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Create NFS Mounts
      import_tasks: ../tasks/nfs_mounts.yaml

    - name: Install docker services
      import_tasks: ../tasks/docker_setup.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Firewalld Rules
      ansible.posix.firewalld:
        port: "{{ item.port }} "
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - port: 8123/tcp

    - name: Start containers
      import_tasks: ../tasks/containers.yaml
