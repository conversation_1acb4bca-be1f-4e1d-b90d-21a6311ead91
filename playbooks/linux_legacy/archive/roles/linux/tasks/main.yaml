---

- name: Install the base list of packages
  import_tasks: debian_init.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: "Ensure ssh key exists for {{ my_user }}"
  openssh_keypair:
    path: "{{ lookup('env','HOME') + '/.ssh/id_ed25519' }}"
    type: ed25519
    owner: "{{ my_user }}"
    group: "{{ my_user }}"
  delegate_to: localhost
  run_once: true

- name: "Copy {{ my_user }} user ssh keys"
  authorized_key:
    user: "{{ lookup('env', 'USER') }}"
    state: present
    key: "{{ lookup('file', lookup('env','HOME') + '/.ssh/id_ed25519.pub') }}"

- name: Install the base list of packages
  import_tasks: debian_base_pkgs.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: Media Capture Host
  import_tasks: media-capture.yaml
  when: ansible_facts['hostname']|lower == 'media-capture'

- name: Burner Host
  import_tasks: burner.yaml
  when: ansible_facts['hostname']|lower == 'burner'
