---

# Run:
#   CWD: ansible/playbooks/linux_plays/
#   ansible-playbook -v -l debianServers -kK roles/linux/tasks/debian_init.yaml

# This assumes a netinstall iso file was used with the following settings
# - Enter 3 times for region, hostname set, domain set, root password and user krizzo set
# - SSH server and standard system utilities installed only

- name: Init a fresh Debian linux install (netinstall with core system and ssh) requires ask-pass and become-pass
  become: yes
  become_method: su
  become_user: root
  become_flags: '-'
  gather_facts: False
  hosts:
  - all

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user: "krizzo"
    sudoers_path: "/etc/sudoers.d/"

  vars_prompt:
    - name: "new_passwd"
      prompt: "New password"
      private: yes

  tasks:
    - name: Getting sudoers file status for {{ my_user }}
      stat:
        path: "{{ sudoers_path }}{{ my_user }}"
      register: sudoers_file

    - name: Adding {{ my_user }} user if missing
      user:
        name: "{{ my_user }}"
        comment: <PERSON> (kriz<PERSON>) Rizzo
        uid: 1000
        group: "{{ my_user }}"
      # when: user doesn't exist

    - name: Install sudo requires ask-pass and become-pass
      ansible.builtin.apt:
        name: sudo
        state: present

    - name: Setting {{ my_user }} in sudoers
      lineinfile:
        dest: "{{ sudoers_path }}{{ my_user }}"
        line: '{{ my_user }} ALL=(ALL) NOPASSWD: ALL'
        create: yes
        owner: root
        group: root
        mode: "0440"
        state: present
        validate: 'visudo -c -f %s'
      when: sudoers_file.stat.exists == false

    - name: Changing {{ my_user }} user password
      user:
        name: "{{ my_user }}"
        password: "{{ new_passwd|password_hash('sha512') }}"

    - name: Changing root user password
      user:
        name: root
        password: "{{ new_passwd|password_hash('sha512') }}"

# - name: Getting ssh dir status
#   stat:
#     path: "{{ ssh_key_path }}"
#   register: ssh_dir

# - name: Getting ssh key file status
#   stat:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#   register: sshkey_file
#   when: ssh_dir.stat.exists == true

# - name: Create .ssh dir for {{ my_user }}
#   file:
#     path: "{{ ssh_key_path }}"
#     state: directory
#     mode: '0700'
#     owner: "{{ my_user }}"
#     group: "{{ my_user }}"
#   when: ssh_dir.stat.exists == false

# - name: Generating SSH key for {{ my_user }}
#   community.crypto.openssh_keypair:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#     type: "{{ ssh_key_type }}"
#     ssh_key_passphrase: "{{ new_passwd }}"
#     ssh_key_comment: "{{ ssh_key_comment }}"
#   when: sshkey_file.stat.exists == false

# - name: Setting ssh dir and file permissions for {{ my_user }}
