---

- name: Check current docker-compose version
  command: docker-compose --version
  register: docker_compose_current_version
  changed_when: false
  failed_when: false

- name: Debug docker-compose version
  debug:
    msg: "{{ docker_compose_ver }} AND {{ docker_compose_current_version }}"

- name: Delete existing docker-compose version if it's older
  file:
    path: "{{ docker_compose_path }}"
    state: absent
  when: >
    docker_compose_current_version.stdout is defined
    and docker_compose_ver not in docker_compose_current_version.stdout

- name: Install Docker Compose (if configured).
  get_url:
    url: https://github.com/docker/compose/releases/download/{{ docker_compose_ver }}/docker-compose-Linux-x86_64
    dest: "{{ docker_compose_path }}"
    mode: 0755
