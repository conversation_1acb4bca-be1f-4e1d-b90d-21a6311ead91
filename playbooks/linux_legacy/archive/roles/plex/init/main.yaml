---

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base insatll:
#     ansible-playbook -l plex -kK roles/rocky/init/main.yaml
#   Plex Install:
#     ansible-playbook -l plex roles/plex/init/main.yaml
#   Update Containers:
#     ansible-playbook -l plex roles/plex/init/main.yaml --start-at-task="Start containers"


- name: Set up plex server
  become: true
  gather_facts: true
  hosts: plex

  # vars_files:
  #   - vars.yaml

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user_name: "krizzo"
    my_user_group: "krizzo"
    docker_compose_ver: "v2.0.0" # See https://github.com/docker/compose/releases
    docker_compose_path: /usr/local/bin/docker-compose

  tasks:
    - name: Import base pkgs
      import_tasks: ../tasks/pkgs.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'
            or ansible_facts['os_family']|lower == 'redhat'

    - name: Create NFS Mounts
      import_tasks: ../tasks/nfs_mounts.yaml

    - name: Install docker services
      import_tasks: ../tasks/docker_setup.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Firewalld Rules
      ansible.posix.firewalld:
        port: "{{ item.port }} "
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - port: 32400/tcp
        - port: 3005/tcp
        - port: 8324/tcp
        - port: 32469/tcp
        - port: 1900/udp
        - port: 32410/udp
        - port: 32412/udp
        - port: 32413/udp
        - port: 32414/udp

    - name: StartContainers
      import_tasks: ../tasks/containers.yaml
