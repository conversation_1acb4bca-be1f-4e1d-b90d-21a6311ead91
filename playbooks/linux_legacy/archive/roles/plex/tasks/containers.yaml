---

- name: Start plex container
  community.docker.docker_container:
    name: plex
    image: "plexinc/pms-docker:plexpass"
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - 32400:32400/tcp
      - 3005:3005/tcp
      - 8324:8324/tcp
      - 32469:32469/tcp
      - 1900:1900/udp
      - 32410:32410/udp
      - 32412:32412/udp
      - 32413:32413/udp
      - 32414:32414/udp
    shm_size: 16G
    volumes:
      - /mnt/nasa-p1z2-docker-data-plex-config:/config
      - /dev/shm:/transcode # Offload transcoding to RAM, requires a lot of memory on the VM
      - /mnt/nasa-p1z2-media-movies:/data/movies
      - /mnt/nasa-p1z2-media-series:/data/series
      - /mnt/nasa-p1z2-media-music:/data/music
    env:
      TZ: "America/Denver"
      PLEX_CLAIM: "<claimToken>"
      ADVERTISE_IP: "http://*************:32400/"
      HOSTNAME: "plex.krizzo.io"
      PLEX_UID: "1000"
      PLEX_GID: "1000"

- name: Start tautulli container
  community.docker.docker_container:
    name: tautulli
    image: ghcr.io/linuxserver/tautulli
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - "8181:8181"
    volumes:
      - /mnt/nasa-p1z2-docker-data-tautulli-config:/config
      - /mnt/nasa-p1z2-docker-data-plex-config-logs-ro:/logs,ro
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
