---

- name: Mount Movies NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/Media/movies
    path: /mnt/nasa-p1z2-media-movies
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Series NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/Media/series
    path: /mnt/nasa-p1z2-media-series
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Music NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/Media/music
    path: /mnt/nasa-p1z2-media-music
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Plex Config NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/plex/config
    path: /mnt/nasa-p1z2-docker-data-plex-config
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Plex Transcode TMP NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/plex/transcode-tmp
    path: /mnt/nasa-p1z2-docker-data-plex-transcode-tmp
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Tautulli Config NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/tautulli/config
    path: /mnt/nasa-p1z2-docker-data-tautulli-config
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount Plex Log RO NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/plex/config/Library/Application Support/Plex Media Server/Logs
    path: /mnt/nasa-p1z2-docker-data-plex-config-logs-ro
    opts: ro,nolock,soft
    state: mounted
    fstype: nfs
