---

    - name: Plex Movies Volume
      become: false
      containers.podman.podman_volume:
        state: present
        name: plex-movies
        label:
          key: plex-movies
        options:
          - "device=/mnt/nasa-p1z2/Media/movies"
          - "type=nfs"
          - "o='addr=192.168.28.31,nolock,soft,rw'"

    - name: Plex Container
      become: false
      containers.podman.podman_container:
        userns: keep-id
        name: plex
        image: ghcr.io/linuxserver/plex
        state: started
        env:
          PUID: "1000"
          PGID: "1000"
          VERSION: "latest"
        ports:
          - "32400:32400"
          - "32400:32400/udp"
          - "32469:32469"
          - "32469:32469/udp"
          - "5353:5353/udp"
          - "1900:1900/udp"
        volume:
          - "plex-movies:/data/movies:Z:U"

    # volumes:
      # - type: volume
      #   source: plex-series
      #   target: /data/series
      #   volume:
      #     nocopy: true
      # - type: volume
      #   source: plex-music
      #   target: /data/music
      #   volume:
      #     nocopy: true
      # - type: volume
      #   source: plex-config
      #   target: /config
      #   volume:
      #     nocopy: true
    # devices:
    #   - "/dev/dvb"
        # Capture device
