# Addtional information

## HDD passthrough

From the xcp-ng server. Example 2TB HD drive is /dev/sdd and 500G SD is /dev/sdc

```bash
cfdisk /dev/sdc
cfdisk /dev/sdd

ls -l /dev/disk/by-id/ | grep sdc
ls -l /dev/disk/by-id/ | grep sdd

mkdir /srv/nvrPassDrives
cd /srv/nvrPassDrives
ln -s /dev/sdd
ln -s /dev/sdc

xe sr-create name-label=nvrPassDrives type=udev content-type=disk device-config:location=/srv/nvrPassDrives
```

## In XOA rename the storage drives

In `Home > Storage` select the SR this case `nvrPassDrives`.

Select the `Disks` tab and rename the iscsi devices they will have an ID in the order the drives were added to the dir. The description may also provide the serial number of the drives.

The drives will show up as /dev/xvdX
