---

- name: Start frigate container
  community.docker.docker_container:
    name: frigate
    image: "blakeblackshear/frigate:stable-amd64"
    state: started
    restart: yes
    restart_policy: unless-stopped
    ports:
      - 5000:5000
      - 1935:1935
    volumes:
      - /mnt/nasa-p1z2-docker-data-frigate-config:/config
      - /etc/localtime:/etc/localtime:ro
      # - /mnt/frigate-recordings:/media/frigate
      # - /dev/xvdb:/tmp/cache
    # TPU for camera AI calculations
    # device:
    #   - "/dev/bus/usb:/dev/bus/usb"
    shm_size: 500M
    #Calculate shm for each campera with this. (width * height * 1.5 * 9 + 270480)/1048576
    # Reolink RLC-820 8MP (4k): ( 3840 * 2160 * 1.5 * 9 + 270480)/1048576 = 107.0450592041
    # Reolink RLC-E1Pro 4MP (2k): ( 2560 * 1440 * 1.5 * 9 + 270480)/1048576 = 47.7188873291
    # Axis M3066-V 4MP (2k):  ( 2304 * 1728 * 1.5 * 9 + 270480)/1048576) = 51.5157623291
    # 1*(( 2304 * 1728 * 1.5 * 9 + 270480)/1048576) + 2*(( 3840 * 2160 * 1.5 * 9 + 270480)/1048576) + 1*(( 2560 * 1440 * 1.5 * 9 + 270480)/1048576) = 313.3247680664
    env:
      FRIGATE_RTSP_PASSWORD: "{{ frigate_rtsp_passwd }}"
