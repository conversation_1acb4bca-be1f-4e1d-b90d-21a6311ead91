---

- name: Mount Frigate Config NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/frigate/config
    path: /mnt/nasa-p1z2-docker-data-frigate-config
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

# - name: Mount Frigate Recordings Drive
#   ansible.posix.mount:
#     src: /dev/xvdc1
#     path: /mnt/frigate-recordings
#     state: mounted
#     fstype: ext4
