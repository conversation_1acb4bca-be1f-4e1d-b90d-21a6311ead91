mqtt: # mosquitto server
  host: *************

#detectors:
#  coral:
#    type: edgetpu
#    device: usb

cameras:
  axis-cam-001: # <------ Name the camera
    ffmpeg:
      inputs:
        - path: rtsp://FRIGATE_USER_CHANGEME:FRIGATE_PASSWORD_CHANGEME@**************:554/axis-media/media.amp # <----- Update for your camera example axis camera
          roles:
            - rtmp
            #- detect
    detect:
      enabled: false
      width: 2304 # <---- update for your camera's resolution
      height: 1728 # <---- update for your camera's resolution
