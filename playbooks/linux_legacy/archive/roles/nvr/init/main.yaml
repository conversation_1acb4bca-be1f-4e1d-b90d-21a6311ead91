---

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base insatll:
#     ansible-playbook -l palantiri-rocky -kK roles/rocky/init/main.yaml
#   frigate Install:
#     ansible-playbook -l palantiri-rocky roles/nvr/init/main.yaml

- name: Set up frigate server
  become: true
  gather_facts: true
  hosts: palantiri-rocky

  # vars_files:
  #   - vars.yaml

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user_name: "krizzo"
    my_user_group: "krizzo"
    frigate_rtsp_passwd: "RTSP_PASSWORD_CHANGEME"

  vars_prompt:
    - name: "frigate_rtsp_passwd"
      prompt: "Frigate RTSP Password"
      private: yes
      confirm: yes

  tasks:
    - name: Import base pkgs
      import_tasks: ../tasks/pkgs.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Create NFS Mounts
      import_tasks: ../tasks/nfs_mounts.yaml

    - name: Install docker services
      import_tasks: ../tasks/docker_setup.yaml
      when: ansible_facts['os_family']|lower == 'rocky'
            or ansible_facts['os_family']|lower == 'centos'

    - name: Firewalld Rules
      ansible.posix.firewalld:
        port: "{{ item.port }} "
        permanent: yes
        state: enabled
        immediate: yes
      loop:
        - port: 5000/tcp
        - port: 1935/tcp

    - name: Start containers
      import_tasks: ../tasks/containers.yaml
