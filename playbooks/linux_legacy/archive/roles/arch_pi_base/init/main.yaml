---

# Run:
#   CWD: ansible/playbooks/linux_plays/
#   ansible-playbook -l archLinuxPis -u alarm -k roles/arch/init/arch_init.yaml --check

- name: Setup a basic arch system
  become: yes
  hosts:
  - all

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user: "krizzo"
    sudoers_path: "/etc/sudoers.d/"
    users:
    - name: krizzo
      comment: <PERSON> (krizzo) Rizzo
      gid: 1000

  vars_prompt:
    # - name: "new_passwd"
    #   prompt: "New password"
    #   private: yes
    - name: "new_passwd"
      prompt: "New password"
      private: yes
      confirm: yes
      encrypt: sha512_crypt
      salt_size: 8
    - name: "ssh_key_name"
      prompt: "SSH key filename [id_ed25519]"
      private: no

  tasks:
    - name: Getting sudoers file status for {{ my_user }}
      ansible.builtin.stat:
        path: "{{ sudoers_path }}{{ my_user }}"
      register: sudoers_file

    - name: Adding {{ my_user }} groups
      ansible.builtin.group:
        name: "{{ my_user }}"
        state: present
        gid: 1000

    - name: Adding {{ my_user }} user
      ansible.builtin.user:
        name: "{{ my_user }}"
        comment: Kyle ({{ my_user }}) Rizzo
        uid: 1000
        group: "{{ my_user }}"

    - name: "Setting {{ my_user }} in sudoers"
      ansible.builtin.lineinfile:
        dest: "{{ sudoers_path }}{{ my_user }}"
        line: '{{ my_user }} ALL=(ALL) NOPASSWD: ALL'
        create: yes
        owner: root
        group: root
        mode: "0440"
        state: present
        validate: 'visudo -cf %s'
      when: sudoers_file.stat.exists == false

    - name: Changing {{ my_user }} user password
      ansible.builtin.user:
        name: "{{ my_user }}"
        password: "{{ new_passwd|password_hash('sha512') }}"

    - name: Changing root user password
      ansible.builtin.user:
        name: root
        password: "{{ new_passwd|password_hash('sha512') }}"

    # - name: Remove the tem 'alarm' from arch linux ARM
    #   ansible.builtin.user:
    #     name: alarm
    #     state: absent
    #     remove: yes

# - name: Getting ssh dir status
#   stat:
#     path: "{{ ssh_key_path }}"
#   register: ssh_dir

# - name: Getting ssh key file status
#   stat:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#   register: sshkey_file
#   when: ssh_dir.stat.exists == true

# - name: Create .ssh dir for {{ my_user }}
#   file:
#     path: "{{ ssh_key_path }}"
#     state: directory
#     mode: '0700'
#     owner: "{{ my_user }}"
#     group: "{{ my_user }}"
#   when: ssh_dir.stat.exists == false

# - name: Generating SSH key for {{ my_user }}
#   community.crypto.openssh_keypair:
#     path: "{{ ssh_key_path }}{{ ssh_key_filename }}"
#     type: "{{ ssh_key_type }}"
#     ssh_key_passphrase: "{{ new_passwd }}"
#     ssh_key_comment: "{{ ssh_key_comment }}"
#   when: sshkey_file.stat.exists == false

# - name: Setting ssh dir and file permissions for {{ my_user }}
