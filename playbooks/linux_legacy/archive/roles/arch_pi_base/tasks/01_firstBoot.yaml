# Run for each fresh install of arch linux arm booted from an SD card
# RUN:
#     ansible-playbook -kK -l pi-001 RaspberryPi/01_firstBoot.yaml -u alarm
- name: Setup the OS to be managed by ansible
  hosts: all
  gather_facts: false
  vars:
    default_root_passwd: root
  tasks:
    - name: Init Pacman Keys
      raw: echo {{ default_root_passwd }} | su - root -c "pacman-key --init"
    - name: Populate Pacman Keys
      raw: echo {{ default_root_passwd }} | su - root -c "pacman-key --populate archlinuxarm"
    - name: Update the system
      raw: echo {{ default_root_passwd }} | su - root -c "pacman -Syu --noconfirm"
    - name: Reboot to the updated system
      raw: echo {{ default_root_passwd }} | su - root -c "shutdown --reboot +1 'System is rebooting in 1 min.' || true"
    - name: Wait for the system to become available
      pause:
        seconds: 180
    - name: Install python3 and sudo
      raw: echo {{ default_root_passwd }} | su - root -c "pacman --noconfirm -S sudo python3 rsync vim git base-devel wget"
    - name: Set sudo for alarm user
      raw: "echo {{ default_root_passwd }} | su - root -c \"sed -i '/root ALL=(ALL) ALL/ {s/$/\\nalarm ALL=(ALL) NOPASSWD: ALL/}' /etc/sudoers\""
