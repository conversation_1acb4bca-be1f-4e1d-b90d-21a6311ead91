# Configure a raspberry pi SD card
# RUN:
#     ansible-playbook -kK -l pi-001 RaspberryPi/02_buildYAY.yaml -u alarm
---
- name: Install yay for the AUR Arch Linux ARM
  hosts: all

  vars:
    yay_dir: "/tmp/yay-git"

  tasks:
    - name: Enable NTP, install git
      block:

      - name: Setting NTP
        command: timedatectl set-ntp true

      - name: Install git
        become: yes
        pacman:
          name: git
          update_cache: yes
          state: present

      - name: Install base-devel
        become: yes
        pacman:
          name: base-devel
          update_cache: yes
          state: present

      - name: Let make use more than one proc
        ansible.builtin.lineinfile:
          path: /etc/makepkg.conf
          regexp: '^MAKEFLAGS='
          line: 'MAKEFLAGS="j4"'
      become: true

    - name: Validating {{ yay_dir }} dir exists
      stat:
        path: "{{ yay_dir }}"
      register: yay_clone_dir

    - name: Clone yay repo
      git:
        repo: https://aur.archlinux.org/yay-git.git
        dest: "{{ yay_dir }}"
      when: yay_clone_dir.stat.exists == false

    - name: Build yay
      command: makepkg -si --noconfirm
      args:
        chdir: "{{ yay_dir }}"

    - name: Update yay
      command: yay -Syu
