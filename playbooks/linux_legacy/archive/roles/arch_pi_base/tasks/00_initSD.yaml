# Configure a raspberry pi SD card
# RUN:
#     ansible-playbook roles/arch_pi_base/tasks/00_initSD.yaml
---
- name: Init an SD card with Arch Linux ARM (alarm) for a raspberry pi.
  hosts: localhost
  connection: local
  become: true
  vars:
    drive_name: "sdb"
    boot_partition: "1"
    boot_dir: "/tmp/arch/boot"
    root_partition: "2"
    root_dir: "/tmp/arch/root"
    pi4_64_file: "ArchLinuxARM-rpi-aarch64-latest.tar.gz"

  vars_prompt:
    - name: "storage_type"
      prompt: "Storage Type [SD|SSD]"
      private: no

  tasks:
    - name: Zero the device
      command: dd if=/dev/zero of=/dev/{{ drive_name }} bs=1M count=8

    - name: Partition /dev/{{ drive_name }}
      raw: echo -en "o\np\nn\np\n1\n\n+256M\nt\nc\nn\np\n2\n\n\nw\n" | fdisk /dev/{{ drive_name }}

    - name: "Create boot vfat FS"
      command: mkfs.vfat /dev/{{ drive_name }}{{ boot_partition }}

    - name: Create root ext4 FS
      command: mkfs.ext4 /dev/{{ drive_name }}{{ root_partition }}

    - name: Validating {{ boot_dir }} dir exists
      stat:
        path: "{{ boot_dir }}"
      register: boot_mnt_dir

    - name: Validating {{ root_dir }} dir exists
      stat:
        path: "{{ root_dir }}"
      register: root_mnt_dir

    - name: Create {{ boot_dir }} dir
      file:
        path: "{{ boot_dir }}"
        state: directory
      when: boot_mnt_dir.stat.exists == false

    - name: Create {{ root_dir }} dir
      file:
        path: "{{ root_dir }}"
        state: directory
      when: root_mnt_dir.stat.exists == false

    # Use ansible 'command' rather than built in 'mount' module to avoid adding to fstab
    - name: Mount the boot partition {{ drive_name }}{{ boot_partition }} to {{ boot_dir }}
      command: mount /dev/{{ drive_name }}{{ boot_partition }} {{ boot_dir }}

    - name: Mount the root partition {{ drive_name }}{{ root_partition }} to {{ root_dir }}
      command: mount /dev/{{ drive_name }}{{ root_partition }} {{ root_dir }}

    - name: Extract arch image to {{ root_dir }} directory.
      unarchive:
        src: http://os.archlinuxarm.org/os/{{ pi4_64_file }}
        dest: '{{ root_dir }}'
        remote_src: yes

    - name: Change the MMC block as pi4s are different for SD
      ansible.builtin.replace:
        path: "{{ root_dir }}/etc/fstab"
        regexp: 'mmcblk0'
        replace: 'mmcblk1'
      when: storage_type == "SD"

    - name: Change the boot drive to SDA
      ansible.builtin.replace:
        path: "{{ root_dir }}/etc/fstab"
        regexp: 'mmcblk0p1'
        replace: 'sda1'
      when: storage_type == "SSD"

    - name: Sync the filesystem after extraction of tar
      command: sync

    - name: Moving the boot files from {{ root_dir }}/boot/* to {{ boot_dir }}
      raw: "mv {{ root_dir }}/boot/* {{ boot_dir }}"

    - name: Unmounting {{ root_dir }} and {{ boot_dir }}
      command: umount {{ root_dir }} {{ boot_dir }}
