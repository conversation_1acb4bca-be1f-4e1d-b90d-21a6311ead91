---

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   ansible-playbook -l silmarillion_temp roles/deb-dhcp/tasks/main.yaml

- name: Set up isc-dhcp-server service
  become: true
  # gather_facts: true
  hosts: silmarillion_temp

  vars_files:
    - vars.yaml

  pre_tasks:
    - name: Update apt cache if needed
      ansible.builtin.apt:
        update_cache: true
        cache_valid_time: 3600

  handlers:
    - name: Restart dhcpd
      ansible.builtin.service:
        name: isc-dhcp-server
        state: restarted

  tasks:
    - name: Ensure isc-dhcp-server present
      ansible.builtin.apt:
        name: isc-dhcp-server
        state: present

    # IPTABLES  -I INPUT -i $LAN_IFACE -p udp --dport 67:68 --sport 67:68 -j ACCEPT
    - name: ALLOW BOOTPS and BOOTPC in IPTABLES
      ansible.builtin.iptables:
          chain: INPUT
          in_interface: "{{ dhcpd_v4_ints_list }}"
          protocol: udp
          source_port: "{{ item.port }}"
          destination_port: "{{ item.port }}"
          jump: ACCEPT
          comment: "ALLOW {{ item.comment }}"
      loop:
        - port: 67
          comment: "BOOTPS"
        - port: 68
          comment: "BOOTPC"

    - name: Create networks dir
      ansible.builtin.file:
        path: "{{ networks_conf_path }}"
        state: directory
        mode: '0755'

    - name: Copy dhcpd base config files
      ansible.builtin.copy:
        src: "{{ item.src }}"
        dest: "{{ item.dst }}"
      loop:
        - src: ../files/dhcpd.conf
          dst: "/etc/dhcp/dhcpd.conf"
        - src: ../files/dhcpd6.conf
          dst: "/etc/dhcp/dhcpd6.conf"
      notify: Restart dhcpd

    - name: Copy pool conf files
      ansible.builtin.copy:
        src: ../files/{{ item }}.conf
        dest: "{{ networks_conf_path }}/{{ item }}.conf"
      loop:
        - "trust_v4"
        - "management_v4"
        - "security_v4"
        - "guest_v4"
        - "trust_v6"
        - "management_v6"
        - "security_v6"
        - "guest_v6"
        - "piavpn_v4"
      notify: Restart dhcpd

    # Space separated interface name list
    - name: Concatenate the ints list for DHCP config v4
      set_fact:
        dhcpd_v4_ints_str: "{{ dhcpd_v4_ints_list | join(' ') }}"

    - name: Set v4 listening interface(s)
      ansible.builtin.replace:
        path: "{{ dhcpd_defaults_file }}"
        regexp: '(^INTERFACESv4=)"(.*)"$'
        replace: '\1"{{ dhcpd_v4_ints_str }}"'
      notify: Restart dhcpd

    - name: Concatenate the ints list for DHCP config v6
      set_fact:
        dhcpd_v6_ints_str: "{{ dhcpd_v6_ints_list | join(' ') }}"

    - name: Set v6 listening interface(s)
      ansible.builtin.replace:
        path: "{{ dhcpd_defaults_file }}"
        regexp: '(^INTERFACESv6=)"(.*)"$'
        replace: '\1"{{ dhcpd_v6_ints_str }}"'
      notify: Restart dhcpd

    - name: Starting dhcpd service
      ansible.builtin.service:
        name: isc-dhcp-server
        enabled: true
        state: started
