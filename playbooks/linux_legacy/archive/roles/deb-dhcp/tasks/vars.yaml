---

dhcpd_v4_config_file: "/etc/dhcp/dhcpd.conf"
dhcpd_v6_config_file: "/etc/dhcp/dhcpd6.conf"
networks_conf_path: "/etc/dhcp/networks"
dhcpd_defaults_file: "/etc/default/isc-dhcp-server"

# Some reason people don't like the log leases files to be messed with.
lease_files_dir: "/var/lib/dhcp/"
lease_files:
  - "dhcpd-trust.leases"
  - "dhcpd-security.leases"
  - "dhcpd-guests.leases"
  - "dhcpd-management.leases"

# Internet needs an IP address
#   sudo ip link set ens224 up
#   sudo ip addr add *************/24 dev ens224
dhcpd_v4_ints_list:
  - "eth0"
dhcpd_v6_ints_list:
  - ""
