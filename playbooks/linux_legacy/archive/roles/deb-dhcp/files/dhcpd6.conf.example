# Server configuration file example for DHCPv6
# From the file used for TAHI tests - addresses chosen
# to match TAHI rather than example block.

# IPv6 address valid lifetime
#  (at the end the address is no longer usable by the client)
#  (set to 30 days, the usual IPv6 default)
default-lease-time 2592000;

# IPv6 address preferred lifetime
#  (at the end the address is deprecated, i.e., the client should use
#   other addresses for new connections)
#  (set to 7 days, the  usual IPv6 default)
preferred-lifetime 604800;

# T1, the delay before Renew
#  (default is 1/2 preferred lifetime)
#  (set to 1 hour)
option dhcp-renewal-time 3600;

# T2, the delay before Rebind (if Renews failed)
#  (default is 3/4 preferred lifetime)
#  (set to 2 hours)
option dhcp-rebinding-time 7200;

# Enable RFC 5007 support (same than for DHCPv4)
allow leasequery;

# Global definitions for name server address(es) and domain search list
option dhcp6.name-servers 3ffe:501:ffff:100:200:ff:fe00:3f3e;
option dhcp6.domain-search "test.example.com","example.com";

# Set preference to 255 (maximum) in order to avoid waiting for
# additional servers when there is only one
##option dhcp6.preference 255;

# Server side command to enable rapid-commit (2 packet exchange)
##option dhcp6.rapid-commit;

# The delay before information-request refresh
#  (minimum is 10 minutes, maximum one day, default is to not refresh)
#  (set to 6 hours)
option dhcp6.info-refresh-time 21600;

# Static definition (must be global)
#host myclient {
#       # The entry is looked up by this
#       host-identifier option
#               dhcp6.client-id 00:01:00:01:00:04:93:e0:00:00:00:00:a2:a2;
#
#       # A fixed address
#       fixed-address6 3ffe:501:ffff:100::1234;
#
#       # A fixed prefix
#       fixed-prefix6 3ffe:501:ffff:101::/64;
#
#       # Override of the global definitions,
#       # works only when a resource (address or prefix) is assigned
#       option dhcp6.name-servers 3ffe:501:ffff:100:200:ff:fe00:4f4e;
#
#       # For debug (to see when the entry statements are executed)
#       #  (log "sol" when a matching Solicitation is received)
#       ##if packet(0,1) = 1 { log(debug,"sol"); }
#}
#
#host otherclient {
#        # This host entry is hopefully matched if the client supplies a DUID-LL
#        # or DUID-LLT containing this MAC address.
#        hardware ethernet 01:00:80:a2:55:67;
#
#        fixed-address6 3ffe:501:ffff:100::4321;
#}

# The subnet where the server is attached
#  (i.e., the server has an address in this subnet)
#subnet6 3ffe:501:ffff:100::/64 {
#       # Two addresses available to clients
#       #  (the third client should get NoAddrsAvail)
#       range6 3ffe:501:ffff:100::10 3ffe:501:ffff:100::11;
#
#       # Use the whole /64 prefix for temporary addresses
#       #  (i.e., direct application of RFC 4941)
#       range6 3ffe:501:ffff:100:: temporary;
#
#       # Some /64 prefixes available for Prefix Delegation (RFC 3633)
#       prefix6 3ffe:501:ffff:100:: 3ffe:501:ffff:111:: /64;
#}

# A second subnet behind a relay agent
#subnet6 3ffe:501:ffff:101::/64 {
#       range6 3ffe:501:ffff:101::10 3ffe:501:ffff:101::11;
#
#       # Override of the global definitions,
#       # works only when a resource (address or prefix) is assigned
#       option dhcp6.name-servers 3ffe:501:ffff:101:200:ff:fe00:3f3e;
#
#}

# A third subnet behind a relay agent chain
#subnet6 3ffe:501:ffff:102::/64 {
#       range6 3ffe:501:ffff:102::10 3ffe:501:ffff:102::11;
#}
