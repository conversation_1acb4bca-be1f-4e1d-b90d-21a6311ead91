subnet 192.168.28.0 netmask 25*********** {
    range ************** **************;
    option subnet-mask 25***********;
    option routers ************;
    option domain-name-servers *******, *******;
    default-lease-time 28800; # 8 hours
    max-lease-time 86400; # 1 day
}

# lease-file-name "/var/lib/dhcp/dhcpd-trust.leases";

# Device prefix breakouts
# Network/Servers: 1-127
# User Static/Reserved: 128-199
# User Dynamic: 200-254
##########################
## Network gear and out of band management
##########################

host og01-a01-krizzo01 {
#    hardware ethernet 00:13:c6:02:0a:79;
     hardware ethernet 00:13:c6:01:a5:6e;
    fixed-address ************0;
}

host printerMC362-wireless {
    hardware ethernet 00:25:36:36:a5:02;
    fixed-address ************2;
}



##########################
## Servers
##########################

host dhcp {
    hardware ethernet be:f2:c9:e5:17:28;
    fixed-address *************;
}

# Host set to static
#host dhcp {
#    hardware ethernet 00:0c:29:a4:8a:21;
#    fixed-address *************;
#}

# Host set to static
#host nasa {
#    hardware ethernet f4:52:14:80:ab:60;
#    fixed-address *************;
#}

# host xen-orchestra-001 {
#     hardware ethernet 22:06:b3:6a:f6:b2;
#     fixed-address *************;
# }

host palantiri-win10 {
    # Windows 10 Blue Iris NVR Camears
    hardware ethernet 6a:4e:02:8a:b6:c0;
    fixed-address *************;
}

host palantiri-rocky {
    # Linux NVR Camears, frigate or viseron
    hardware ethernet 56:55:eb:fb:e8:6a;
    fixed-address *************;
}

host bombadil {
    hardware ethernet 00:0c:29:22:11:8b;
    fixed-address *************;
}

host plex {
    hardware ethernet a2:c9:73:9a:f6:14;
    fixed-address *************;
}



##########################
## IoT Devices Chromcast, Nest, Ring, Raspberry PIs
##########################

host ospi {
    hardware ethernet b8:27:eb:a5:89:54;
    fixed-address *************;
}

host octopi {
    hardware ethernet dc:a6:32:0e:e2:ab;
    fixed-address *************;
}

host status-cube {
    hardware ethernet b8:27:eb:1d:e8:b5;
    fixed-address *************;
}

host pi-001 {
    hardware ethernet dc:a6:32:e7:5d:77;
    fixed-address *************;
}

host pi-002 {
    hardware ethernet dc:a6:32:e7:5d:d1;
    fixed-address *************;
}

host pi-003 {
    hardware ethernet dc:a6:32:e9:54:2f;
    fixed-address *************;
}

host pi-004 {
    hardware ethernet dc:a6:32:e9:53:e1;
    fixed-address *************;
}

host BeagleBoneBlack {
    hardware ethernet a8:10:87:b3:c0:57;
    fixed-address *************;
}

host Chromecast-Ultra-MasterTV {
    hardware ethernet 48:d6:d5:09:44:c8;
    fixed-address *************;
}

host Chromecast-V2-FamilyTV {
    hardware ethernet 48:d6:d5:0f:b5:ce;
    fixed-address *************;
}

host Chromecast-Ultra-PlayroomTV {
    hardware ethernet f8:0f:f9:43:04:0f;
    fixed-address *************;
}

host Chamberlain-Garage-Opener {
    hardware ethernet 64:52:99:0e:aa:4b;
    fixed-address *************;
}

host Lilly_ESP32_Bed_Lights {
    hardware ethernet 24:62:ab:fd:70:18;
    fixed-address *************;
}

host Tyler_ESP32_Bed_Lights {
    hardware ethernet 24:62:ab:fb:02:b0;
    fixed-address *************;
}

host Florence_ESP32_Bed_Lights {
    hardware ethernet 24:62:ab:fc:ff:8c;
    fixed-address *************;
}

##########################
## Desktops, Laptops, Phones
##########################

host krizzo-arch-desktop {
    hardware ethernet f0:92:1c:e8:e6:8b;
    fixed-address ************28;
}

host krizzo-win10-desktop {
    hardware ethernet 00:d8:61:1a:30:b2;
    fixed-address ************29;
}

host krizzo-w530-laptop-wired {
    hardware ethernet 3c:97:0e:ba:03:e9;
    fixed-address ************30;
}

host Anker-DarkGray-USBC-adaptor {
    hardware ethernet 00:e0:4c:02:14:04;
    fixed-address ************31;
}

host pixelxl-phone {
    hardware ethernet ac:37:43:df:1b:d4;
    fixed-address ************32;
}

host pixel3a-srizzo-phone {
    hardware ethernet 58:cb:52:46:31:fc;
    fixed-address ************33;
}

host pixel5-krizzo-phone {
    hardware ethernet 6e:d9:a5:6f:80:bc;
    fixed-address ************34;
}

host Q-C02C801LMD6T-krizzo {
    # Belkin USB-C to Ethernet
    # Work laptop Adaptor
    hardware ethernet c4:41:1e:76:82:23;
    fixed-address ************35;
}

host lilly-desktop-wired {
    hardware ethernet 98:fa:9b:b7:1c:93;
    fixed-address ************36;
}

host lilly-desktop-wifi {
    hardware ethernet e8:4e:06:63:f3:55;
    fixed-address ************37;
}

host tyler-desktop-wired {
    hardware ethernet 98:fa:9b:b7:18:14;
    fixed-address ************38;
}

host tyler-desktop-wifi {
    hardware ethernet e8:4e:06:63:f3:86;
    fixed-address ************39;
}

host nintendo-switch-001 {
    hardware ethernet b8:8a:ec:7d:e5:3b;
    fixed-address ************40;
}

host nintendo-switch-002 {
    hardware ethernet 74:f9:ca:06:a2:58;
    fixed-address ************41;
}

host krizzo-w530-laptop-wireless {
    hardware ethernet 3c:a9:f4:34:a3:d0;
    fixed-address **************;
}
