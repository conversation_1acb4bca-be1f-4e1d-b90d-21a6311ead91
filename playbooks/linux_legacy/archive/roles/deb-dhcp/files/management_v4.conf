subnet ************ netmask ************* {
    range ************** **************;
    option subnet-mask *************;
    option routers ************;
    option domain-name-servers *******, *******;
    default-lease-time 3600; # 1 hour
    max-lease-time 3600; # 1 hour
}

# lease-file-name "/var/lib/dhcp/dhcpd-management.leases";

##########################
## Network gear and out of band management
##########################

#host og01-a01-krizzo01 {
#    hardware ethernet 00:13:c6:02:0a:79;
#    fixed-address *************;
#}

# Host set to static
#host ipmi-nasa {
#    hardware ethernet 0c:c4:7a:67:8f:51;
#    fixed-address *************;
#}

# Host set to static
#host ipmi-esxi-001 {
#    hardware ethernet 0c:c4:7a:6d:ef:31;
#    fixed-address *************;
#}

# Host set to static
#host esxi-001 {
#    hardware ethernet f4:52:14:7b:27:b0;
#    fixed-address *************;
#}

# Host set to static
host xen-orchestra-001 {
    hardware ethernet 22:06:b3:6a:f6:b2;
    fixed-address *************;
}

host unifi-ap-ac-lr {
    hardware ethernet 04:18:d6:c0:1d:84;
    fixed-address *************;
}

host unifi-ap-ac-lite {
    hardware ethernet 04:18:d6:c0:1e:46;
    fixed-address *************;
}

host xcp-ng-desktop001 {
    hardware ethernet 10:60:4b:5b:4f:be;
    fixed-address *************0;
}

host xcp-ng-srv001 {
    hardware ethernet f4:52:14:7b:27:b0;
    fixed-address **************;
}
