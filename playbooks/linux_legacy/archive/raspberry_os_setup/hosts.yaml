# vim: set syntax=yaml:

---
all:
  hosts:
  children:
    linuxMachines:
      hosts:
      children:
        debServers:
        archLinuxARM:
    archLinuxARM:
      hosts:
      children:
        pi4s:
        archLinuxBBB:
    pi4s:
      hosts:
        pi-001:
          ansible_host: *************
        pi-002:
          ansible_host: *************
        pi-003:
          ansible_host: *************
        pi-004:
          ansible_host: *************
    debServers:
      hosts:
        media-capture:
          ansible_host: *************
        bastion:
          ansible_host: *************
        burner:
          ansible_host: **************
    archLinuxBBB:
      hosts:
        beagleBoneBlack:
          ansible_host: **************
    testServers:
      hosts:
        debTestServer:
            ansible_host: **************
        arch_arm_pi:
            ansible_host: *************
