# Configure the eeprom to netboot and enable UART (pins 14/15)
# RUN:
#     ansible-playbook -v -kK -u pi -l pi-003 RaspberryPi/01_eepromConfig.yaml
- name: Setup the OS to be managed by ansible
  hosts: all
  gather_facts: true
  become: true
  vars:
    eeprom_release_file: /etc/default/rpi-eeprom-update
    eeprom_version: "2021-02-16"
    eeprom_url: "https://github.com/raspberrypi/rpi-eeprom/raw/master/firmware/stable/"
    local_log_pre: /tmp/{{ inventory_hostname }}_pre_upgrade.txt
    local_log_post: /tmp/{{ inventory_hostname }}_post_upgrade.txt

  tasks:
    - name: Display all variables/facts known for a host
      ansible.builtin.debug:
        var: hostvars[inventory_hostname]
        verbosity: 2

    - name: Set eeprom to stable
      ansible.builtin.replace:
        path: "{{ eeprom_release_file }}"
        regexp: 'critical'
        replace: 'stable'

    - name: Update and Upgrade
      ansible.builtin.apt:
        update_cache: yes
        upgrade: full

    - name: Get the Pi serial number
      # raw: 'cat /proc/cpuinfo | grep Serial | awk -F ": " "{print $2}" | tail -c 8'
      ansible.builtin.command: 'grep -Po "^Serial\s*:\s*\K[[:xdigit:]]{16}" /proc/cpuinfo'
      register: pi_serial_number

    - name: Get eeprom version
      ansible.builtin.command: rpi-eeprom-update
      register: eeprom_pre_version
      failed_when: eeprom_pre_version.rc >= 2

    - name: Get eeprom settings
      ansible.builtin.command: rpi-eeprom-update
      register: eeprom_pre_settings
      failed_when: eeprom_pre_settings.rc >= 2

    # Get the mac address
    # RUN: ansible -b pi-003 -m setup -u pi -kK -a 'filter=ansible_default_ipv4'
    - name: Write the current eeprom settings and pi info to pre log file
      ansible.builtin.copy:
        content: |
          EEPROM Preupgrade
          {{ eeprom_pre_version.stdout }}

          EEPROM settings:
          {{ eeprom_pre_settings.stdout }}

          ====== Serial Number and MAC Addr ======

          Serial: {{ pi_serial_number.stdout }}
          MAC Addr: {{ hostvars[inventory_hostname].ansible_default_ipv4.macaddress }}

        dest: "{{ local_log_pre }}"
      delegate_to: localhost

    - name: Set UART and Netboot (PXE)
      ansible.builtin.get_url:
        url: "{{ eeprom_url }}pieeprom-{{ eeprom_version }}.bin"
        dest: "/tmp/pieeprom-{{ eeprom_version }}.bin"

    - name: Write /tmp/bootconf.txt file
      ansible.builtin.copy:
        dest: "/tmp/bootconf.txt"
        content: |
          [all]
          BOOT_UART=1
          WAKE_ON_GPIO=1
          POWER_OFF_ON_HALT=0
          DHCP_TIMEOUT=45000
          DHCP_REQ_TIMEOUT=4000
          TFTP_FILE_TIMEOUT=30000
          TFTP_IP=
          TFTP_PREFIX=0
          BOOT_ORDER=0xf241
          SD_BOOT_MAX_RETRIES=3
          NET_BOOT_MAX_RETRIES=5
          [none]
          FREEZE_VERSION=0"

    - name: Create bin file
      ansible.builtin.command: rpi-eeprom-config --out /tmp/pieeprom-{{ eeprom_version }}-netboot-uart.bin --config /tmp/bootconf.txt /tmp/pieeprom-{{ eeprom_version }}.bin

    - name: Flash new bin with Netboot and uart enabled
      ansible.builtin.command: rpi-eeprom-update -d -f /tmp/pieeprom-{{ eeprom_version }}-netboot-uart.bin

    - name: Rebooting for eeprom update
      reboot:

    - name: Get eeprom version after upgrade
      ansible.builtin.command: rpi-eeprom-update
      register: eeprom_post_version
      failed_when: eeprom_post_version.rc >= 2

    - name: Get eeprom settings
      ansible.builtin.command: rpi-eeprom-update
      register: eeprom_post_settings
      failed_when: eeprom_post_settings.rc >= 2

    # Get the mac address
    # RUN: ansible -b pi-003 -m setup -u pi -kK -a 'filter=ansible_default_ipv4'
    - name: Write the current eeprom settings and pi info to post log file
      ansible.builtin.copy:
        content: |
          EEPROM after Upgrade:
          {{ eeprom_post_version.stdout }}

          EEPROM settings:
          {{ eeprom_post_settings.stdout }}

        dest: "{{ local_log_post }}"
      delegate_to: localhost
