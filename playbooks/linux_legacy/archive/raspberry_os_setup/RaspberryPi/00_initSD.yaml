# Configure a raspberry pi SD card
# RUN:
#     ansible-playbook -vv RaspberryPi/00_initSD.yaml
---
- name: Init an SD card with Raspberry Pi OS Lite
  hosts: localhost
  connection: local
  vars:
    drive_name: "sdb"
    boot_partition: "1"
    boot_dir: "/tmp/rpios/boot"
    tmp_img_dir: "/tmp"
    image_name: "2021-01-11-raspios-buster-armhf-lite"
    image_url: "https://downloads.raspberrypi.org/raspios_lite_armhf/images/raspios_lite_armhf-2021-01-12/{{ image_name }}.zip"
  become: true

  tasks:
    - name: Zero the device
      ansible.builtin.command: dd if=/dev/zero of=/dev/{{ drive_name }} bs=1M count=8

    - name: "Extract arch image to {{ tmp_img_dir }} directory"
      ansible.builtin.unarchive:
        src: '{{ image_url }}'
        dest: '{{ tmp_img_dir }}'
        remote_src: yes
        list_files: yes
      register: file_info

    - name: "Copy img {{ tmp_img_dir }}/{{ file_info.files[0] }} to device /dev/{{ drive_name }}"
      ansible.builtin.command: dd if={{ tmp_img_dir }}/{{ file_info.files[0] }} of=/dev/{{ drive_name }} bs=4M conv=fsync

    - name: "Validating {{ boot_dir }} dir exists"
      ansible.builtin.stat:
        path: "{{ boot_dir }}"
      register: boot_mnt_dir

    - name: "Create {{ boot_dir }} dir"
      ansible.builtin.file:
        path: "{{ boot_dir }}"
        state: directory
      when: boot_mnt_dir.stat.exists == false

    # Use ansible 'command' rather than built in 'mount' module to avoid adding to fstab
    - name: "Mount the boot partition /dev/{{ drive_name }}{{ boot_partition }} to {{ boot_dir }}"
      ansible.builtin.command: mount /dev/{{ drive_name }}{{ boot_partition }} {{ boot_dir }}

    - name: "Ensure 'ssh' file exists in {{ boot_dir }}"
      ansible.builtin.copy:
        content: ""
        dest: "{{ boot_dir }}/ssh"
        force: no

    - name: "Unmounting {{ boot_dir }}"
      ansible.builtin.command: umount {{ boot_dir }}
