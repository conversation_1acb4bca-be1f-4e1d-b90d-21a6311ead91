# Raspberry pi LED status cube

Based on https://there.oughta.be/an/led-cube

## Physical Hardware setups

### Solder the adafruit RGB Matrix bonnet

#### For 64x64 matrix

If using a matrix larger than 32 you'll need 5 channels

Bottom solder jumpers short to 8

#### Better performance Quality

Solder a jumper for GPIO4 and GPIO18

## Install Raspberry Pi OS Lite and Setup

Before even putting it in the pi modify the cmdline.txt to dedicate one CPU to refreshing the displays and turning off the sound as we're using the GPIO it would send signal on.

Add `dtparam=audio=off isolcpus=3` to `/boot/cmdline.txt` at the end of the line no new line

Possibly blacklist the snd_bcm2835 if using arch linux to disable the audio.

### Install the python libraries on the machine that will be monitored and sending metrics

pip install psutil git

## Getting the main code

git clone https://github.com/krizzo/there.oughta.be.git

cd there.oughta.be/led-cube/led-cube

## Install the rgb matrix library for adafruit

### Included in my repo or pull and run from adafruit

sudo bash rgb-matrix.sh

OR

curl https://raw.githubusercontent.com/adafruit/Raspberry-Pi-Installer-Scripts/master/rgb-matrix.sh >rgb-matrix.sh
sudo bash rgb-matrix.sh

Select bonnet and Quality as your options.

## Fix OpenGL rendering on the pi

wget https://raw.githubusercontent.com/matusnovak/rpi-opengl-without-x/master/triangle.c

gcc -c triangle.c -o triangle.o -I/opt/vc/include
gcc -o triangle triangle.o -lbrcmEGL -lbrcmGLESv2 -L/opt/vc/lib

## Build the driver code

g++ -g -o cpu-stats-gl cpu-stats-gl.cpp -std=c++11 -lbrcmEGL -lbrcmGLESv2 -I/opt/vc/include -L/opt/vc/lib -Lrpi-rgb-led-matrix/lib -lrgbmatrix -lrt -lm -lpthread -lstdc++ -Irpi-rgb-led-matrix/include/

## Testing the Displays

sudo ~/there.oughta.be/led-cube/led-cube/rpi-rgb-led-matrix/examples-api-use/demo -D0 \
 --led-gpio-mapping=adafruit-hat-pwm \
 --led-rgb-sequence=RGB \
 --led-cols=64 \
 --led-rows=64 \
 --led-pwm-lsb-nanoseconds=50 \
 --led-panel-type=FM6126A \
 --led-pwm-bits=11 \
 --led-chain=3

## Running the binary listener

sudo ./cpu-stats-gl --led-show-refresh --led-slowdown-gpio=1 --led-pwm-dither-bits=2

sudo ./cpu-stats-gl --led-show-refresh --led-slowdown-gpio=2 --led-pwm-dither-bits=2

Then use `sudo stress --cpu 8 --timeout 30` on a host to verify things are working.
