---

# Run:
#   CWD: ansible/playbooks/linux_plays/
#   ansible-playbook -l debianServers -k base.yaml
#   ansible-playbook -l bombadil base.yaml

- name: Base linux system setup
  become: yes  # Do we really need this at the top level?
  hosts:
  - all

  vars:
    provider:
      server: "{{ ansible_host }}"
      user: "{{ username }}"
      password: "{{ password }}"
      server_port: "{{ ansible_port }}"
    my_user: "krizzo"
    ssh_key_type: "ed25519"
    ssh_key_path: "~/.ssh/"
    ssh_key_filename: "id_personal"
    ssh_key_comment: "{{ my_user }} {{ ssh_key_type }} on {{ ansible_host }} "
    docker_compose_path: /usr/local/bin/docker-compose
    docker_compose_ver: "1.27.4"
    nas_server_ip: *************

  tasks:
    - include_role:
        # name: debian
        name: game-server
    # - name: Install docker on debian host
    #   import_tasks: roles/docker_host/tasks/debian_docker_host.yaml
