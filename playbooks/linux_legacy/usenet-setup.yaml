---
#   sourcer Install:
#     ansible-playbook -l pippin sourcer-setup.yaml
#   Update Containers:
#     ansible-playbook -l pippin sourcer-setup.yaml --start-at-task="sourcer : start_sourcer_container"

- name: Set up sourcer server
  become: true
  gather_facts: true
  hosts: pippin

  tasks:
    - name: setup_usenet_services
      import_role:
        name: usenet-setup
# TODO: Split the whole usenet-setup role into separate roles based on each container. Like the torrent setup.
