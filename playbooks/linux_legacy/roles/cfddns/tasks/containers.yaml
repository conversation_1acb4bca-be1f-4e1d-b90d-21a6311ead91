---
- name: start_cf_ddns
  community.docker.docker_container:
    name: "cf-ddns-{% if item.subdomain == '*' %}star{% elif item.subdomain == '' %}root{% else %}{{ item.subdomain }}{% endif %}.{{ item.zone }}"
    image: oznu/cloudflare-ddns
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    env:
      API_KEY: "{{ cf_ddns_api_key }}"
      ZONE: "{{ item.zone }}"
      SUBDOMAIN: "{{ item.subdomain }}"
      PROXIED: "{{ item.proxied }}"
  loop: "{{ records_list }}"
