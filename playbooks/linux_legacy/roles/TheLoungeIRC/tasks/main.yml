---
# tasks file for TheLoungeIRC

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: create_docker_dirs
  ansible.builtin.file:
    path: "{{item}}"
    state: directory
    mode: "0755"
    owner: "1000"
    group: "1000"
  loop:
    - "/mnt/nasa-p1z2-docker-data/thelounge/config"

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 9000
      protocol: "tcp"
      comment: "The Lounge IRC"

- name: start_containers_DEB
  import_tasks: containers_DEB.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: start_containers_RHEL
  import_tasks: containers_RHEL.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'
