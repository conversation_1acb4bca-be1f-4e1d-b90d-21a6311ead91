---
- name: start_thelounge_container_RHEL
  community.docker.docker_container:
    name: thelounge
    image: "lscr.io/linuxserver/thelounge:latest"
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - 9000:9000/tcp
    volumes:
      - /mnt/nasa-p1z2-docker-data/thelounge/config:/config
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
