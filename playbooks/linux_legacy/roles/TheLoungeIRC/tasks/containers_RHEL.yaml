---
- name: pulling_latest_images_RHEL
  containers.podman.podman_image:
    name: "{{ item }}"
  loop:
    - "lscr.io/linuxserver/thelounge:latest"

- name: stopping_containers_RHEL
  containers.podman.podman_container:
    name: "{{ item }}"
    state: absent
  loop:
    - thelounge

- name: start_thelounge_container_RHEL
  become: false
  containers.podman.podman_container:
    name: thelounge
    image: "lscr.io/linuxserver/thelounge:latest"
    state: started
    restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 9000:9000/tcp
    volumes:
      - /mnt/nasa-p1z2-docker-data/thelounge/config:/config
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
