---
# tasks file for plex

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: create_local_dirs_RHEL
  ansible.builtin.file:
    path: "{{item}}"
    state: directory
    mode: "0755"
    owner: "1000"
    group: "1000"
  loop:
    - "/mnt/local-docker-data-plex-config"
    - "/mnt/local-docker-data-tautulli-config"
    - "/mnt/local-docker-data-plex-transcode"
    - "/mnt/local-docker-data-caddy-config"
    - "/mnt/nasa-p1z2-docker-data/caddy/data"
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 32400
      protocol: "tcp"
      comment: "Plex Media Server"
    - port: 1900
      protocol: "udp"
      comment: "Plex DLNA Server Access"
    - port: 5353
      protocol: "tcp"
      comment: "Plex Bonjour or Avahi network discover"
    - port: 8324
      protocol: "tcp"
      comment: "Plex Roku via Plex Companion"
    - port: 32469
      protocol: "tcp"
      comment: "Plex DLNA Server Access"
    - port: 32410
      protocol: "udp"
      comment: "Plex GDM network discovery port"
    - port: 32412
      protocol: "udp"
      comment: "Plex GDM network discovery port"
    - port: 32413
      protocol: "udp"
      comment: "Plex GDM network discovery port"
    - port: 32414
      protocol: "udp"
      comment: "Plex GDM network discovery port"
    - port: 8181
      protocol: "tcp"
      comment: "Tautulli web interface"
    - port: 80
      protocol: "tcp"
      comment: "Caddy HTTP"
    - port: 443
      protocol: "tcp"
      comment: "Caddy HTTPS"

- name: start_containers_DEB
  import_tasks: containers_DEB.yaml
  when: ansible_facts['os_family']|lower == 'debian'

- name: start_containers_RHEL
  import_tasks: containers_RHEL.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'
