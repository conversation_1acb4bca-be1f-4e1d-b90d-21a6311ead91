---
# defaults file for plex
provider:
  server: "{{ ansible_host }}"
  user: "{{ username }}"
  password: "{{ password }}"
  server_port: "{{ ansible_port }}"

my_user_name: "USERNAME_OR_ID_NUM"
my_user_group: "USERNAME_OR_ID_NUM"

# Caddy configuration
cf_caddy_api_token: "CLOUDFLARE_API_TOKEN"
cf_caddy_email_addr: "CLOUDFLARE_EMAIL_ADDR"
domain_list:
  - name: MY.DOMAIN.COM # curl -i https://MY.DOMAIN.COM --resolve 'MY.DOMAIN.COM:443:CADDY_HOST_IP_ADDR'
    type: "reverse_proxy"
    data: "WEB_HOST_IP_ADDR:SERVICE_PORT"
