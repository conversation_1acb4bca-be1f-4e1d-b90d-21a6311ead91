---
- name: start_deluge_pia_vpn
  containers.podman.podman_container:
    name: deluge
    image: "binhex/arch-delugevpn"
    state: started
    force_restart: yes
    restart_policy: always
    # network: bridge
    sysctl: # Used for WireGuard Setup
      net.ipv4.conf.all.src_valid_mark: 1
    privileged: yes
    # capabilities: # Used for openVPN setup
    #   - NET_ADMIN
    ports:
      - "8112:8112" # expose port for deluge webui
      - "8118:8118" # expose port for privoxy
      - "58846:58846" # expose port for deluge daemon
      # - "58946:58946" # expose port for deluge incoming port (used only if VPN_ENABLED=no)
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/nasa-p1z2-docker-data/deluge/config:/config
      - /mnt/nasa-p1z2-data/torrents:/data
    env:
      VPN_ENABLED: "{{ deluge_vpn_enabled }}"
      VPN_USER: "{{ pia_account_username }}"
      VPN_PASS: "{{ pia_account_password }}"
      VPN_PROV: "{{ deluge_vpn_prov }}"
      VPN_CLIENT: "{{ deluge_vpn_client }}"
      # VPN_CLIENT: openvpn
      STRICT_PORT_FORWARD: "{{ deluge_strict_port_forward }}" # US locations not supported currently use ca-vancouver.privacy.network for WG setup
      ENABLE_PRIVOXY: "{{ deluge_enable_privoxy }}"
      LAN_NETWORK: "{{ deluge_lan_network }}"
      NAME_SERVERS: "{{ deluge_name_servers }}"
      DELUGE_DAEMON_LOG_LEVEL: "{{ deluge_deluge_daemon_log_level }}"
      DELUGE_WEB_LOG_LEVEL: "{{ deluge_deluge_web_log_level }}"
      # IMPORTANT 'VPN_INPUT_PORTS' is NOT to define the incoming port for the VPN,
      # this environment variable is used to define port(s) you want to allow in to the VPN network when network binding multiple containers together,
      # configuring this incorrectly with the VPN provider assigned incoming port COULD result in IP leakage, you have been warned!.
      # VPN_INPUT_PORTS: 1234
      # VPN_OUTPUT_PORTS: 5678
      DEBUG: "{{ deluge_debug }}"
      UMASK: "{{ deluge_umask }}"
      PUID: "{{ deluge_puid }}"
      PGID: "{{ deluge_pgid }}"
