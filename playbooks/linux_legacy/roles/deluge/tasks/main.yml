---
# tasks file for deluge

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 8112
      protocol: "tcp"
      comment: "expose port for deluge webui"
    - port: 8118
      protocol: "udp"
      comment: "expose port for privoxy"
    - port: 58846
      protocol: "tcp"
      comment: "expose port for deluge daemon"
    # - port: 58946
    #   protocol: "tcp"
    #   comment: "expose port for deluge incoming port (used only if VPN_ENABLED=no)"
    # - port: 58946
    #   protocol: "udp"
    #   comment: "expose port for deluge incoming port (used only if VPN_ENABLED=no)"

# Make sure we have the correct modules to run the wireguard VPN
# https://www.digitalocean.com/community/tutorials/how-to-set-up-wireguard-on-rocky-linux-8

- name: install_required_modules
  ansible.builtin.dnf:
    name: "{{ item }}"
    state: latest
    update_cache: yes
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'
  loop:
    - epel-release
    - elrepo-release
    - kmod-wireguard
    - wireguard-tools

- name: add_iptables_mingle_module
  community.general.modprobe:
    name: "{{ item }}"
    state: present
  loop:
    - wireguard
    - iptable_mangle

- name: start_containers
  import_tasks: containers.yaml
