---
# defaults file for deluge
deluge_vpn_enabled: "yes"
deluge_vpn_prov: pia
deluge_vpn_client: wireguard
# deluge_vpn_client: openvpn
deluge_strict_port_forward: "yes" # US locations not supported currently use ca-vancouver.privacy.network for WG setup
deluge_enable_privoxy: "yes"
deluge_lan_network: ************/21
deluge_name_servers: ************,************,*******,************,************,*******
deluge_deluge_daemon_log_level: info
deluge_deluge_web_log_level: info
# IMPORTANT 'VPN_INPUT_PORTS' is NOT to define the incoming port for the VPN,
# this environment variable is used to define port(s) you want to allow in to the VPN network when network binding multiple containers together,
# configuring this incorrectly with the VPN provider assigned incoming port COULD result in IP leakage, you have been warned!.
# deluge_vpn_input_ports: 1234
# deluge_vpn_output_ports: 5678
deluge_debug: "false"
deluge_umask: "000"
deluge_puid: "1000"
deluge_pgid: "1000"
