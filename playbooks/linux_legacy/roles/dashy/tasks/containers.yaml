---
- name: create_dashy_config_dir
  ansible.builtin.file:
    path: "{{ dashy.config_path }}"
    state: directory
    recurse: true
    mode: "0755"
    owner: "{{ my_UID }}"
    group: "{{ my_GID }}"

- name: copy_dashy_config
  ansible.builtin.copy:
    src: "{{ dashy.config_file }}"
    dest: "{{ dashy.config_path }}"
    mode: "0644"
    owner: "{{ my_UID }}"
    group: "{{ my_GID }}"

- name: start_dashy_RHEL
  containers.podman.podman_container:
    name: dashy
    image: "lissy93/dashy"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - "{{ dashy.service_port }}:80" # expose port for dashy webui
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - "{{ dashy.config_path }}/{{ dashy.config_file }}:/app/public/{{ dashy.config_file }}"
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'

- name: start_dashy_DEB
  community.docker.docker_container:
    name: dashy
    image: "lissy93/dashy"
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - "{{ dashy.service_port }}:80" # expose port for dashy webui
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - "{{ dashy.config_path }}/{{ dashy.config_file }}:/app/public/{{ dashy.config_file }}"
  when: ansible_facts['os_family']|lower == 'debian'
