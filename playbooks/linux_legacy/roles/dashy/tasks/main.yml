---
# tasks file for dashy

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: "{{ dashy.service_port }}"
      protocol: "tcp"
      comment: "expose port for dashy webui"

- name: start_containers
  import_tasks: containers.yaml
