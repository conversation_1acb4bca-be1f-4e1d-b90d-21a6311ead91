appConfig:
  theme: one-dark
  layout: auto
  iconSize: small
  language: en
pageInfo:
  title: krizzo Services
  description: ""
  navLinks: []
  footerText: ""
sections:
  - name: Hardware services (network/servers)
    icon: mdi-lan
    items:
      - title: Firewall
        icon: mdi-wall-fire
        url: http://************:5958
        id: 0_3430_firewall
      - title: Unifi Controller
        icon: si-ubiquiti
        url: https://*************:8443
        id: 1_3430_unificontroller
      - title: True NAS IPMI
        icon: mdi-server-off
        url: https://*************
        id: 2_3430_truenasipmi
      - title: XCP-NG Rack Server IPMI
        icon: mdi-server-off
        url: https://*************
        id: 3_3430_xcpngrackserveripmi
      - title: True NAS
        icon: si-truenas
        url: https://*************
        id: 4_3430_truenas
      - title: Xen Orchestra
        icon: mdi-server
        url: https://*************
        id: 5_3430_xenorchestra
      - title: Opengear
        icon: mdi-console-network-outline
        url: https://*************/
        id: 6_3430_opengear
    displayData:
      sortBy: default
      rows: 1
      cols: 1
      collapsed: false
      hideForGuests: false
  - name: Home Services
    icon: mdi-home
    items:
      - title: Home Assistant
        icon: si-homeassistant
        url: http://*************4:8123
        id: 0_1261_homeassistant
      - title: Open Sprinkler System
        icon: mdi-sprinkler
        url: http://*************:8080
        id: 1_1261_opensprinklersystem
      - title: Octoprint 3D Printer
        icon: mdi-printer-3d
        url: http://*************
        id: 2_1261_octoprintdprinter
  - name: Media Services
    items:
      - title: Tautulli
        icon: favicon
        url: http://192.168.28.36:8181
      - title: Ombi
        icon: favicon
        url: http://192.168.28.40:3579
      - title: Overseerr
        icon: favicon
        url: http://192.168.28.40:5055
      - title: Radarr
        icon: favicon
        url: http://192.168.28.40:7878
      - title: Sonarr
        icon: favicon
        url: http://192.168.28.40:8989
      - title: Sabnzbd
        icon: favicon
        url: http://192.168.28.40:8080
      - title: Nzb Hydra 2
        icon: favicon
        url: http://192.168.28.40:5076
      - title: Deluge
        icon: favicon
        url: REPLACE_ICON
  - name: Online Services
    items:
      - title: You Need A Budget
        icon: favicon
        url: https://app.youneedabudget.com
        id: 0_1481_youneedabudget
      - title: Microsoft Family
        icon: favicon
        url: https://account.microsoft.com/family
        id: 1_1481_microsoftfamily
      - title: Google Family
        icon: favicon
        url: https://families.google.com/families
        id: 2_1481_googlefamily
      - title: Google Drive
        icon: favicon
        url: https://drive.google.com
        id: 3_1481_googledrive
      - title: Google Calendar
        icon: favicon
        url: https://calendar.google.com
        id: 4_1481_googlecalendar
  - name: Finance Services
    items:
      - title: You Need A Budget
        icon: favicon
        url: https://app.youneedabudget.com
      - title: Mountain America Credit Union
        icon: favicon
        url: https://www.macu.com/
      - title: Discover CC
        icon: favicon
        url: https://www.discover.com/
      - title: Wells Fargo CC
        icon: favicon
        url: https://www.wellsfargo.com/
  - name: Social Services
    items:
      - title: The Lounge IRC
        icon: favicon
        url: https://*************:9000
  - name: Dev & Cloud
    icon: far fa-code
    items:
      - title: GitHub
        icon: favicon
        url: https://github.com/
        id: 0_892_github
      - title: StackOverflow
        icon: favicon
        url: http://stackoverflow.com/
        id: 1_892_stackoverflow
      - title: CloudFlare
        icon: favicon
        url: https://dash.cloudflare.com/
        statusCheckAcceptCodes: "403"
        id: 2_892_cloudflare
      - title: Regexr
        icon: fas fa-code
        description: App for creating, testing and understanding regular expressions
        url: https://regexr.com/
        id: 3_892_regexr
      - title: Regex101
        icon: fas fa-code
        description: App for creating, testing and understanding regular expressions
        url: https://regex101.com/
        id: 4_892_regex
      - title: Documentation
        subItems:
          - title: Go
            url: https://go.dev/doc
            icon: si-go
            color: "#00ADD8"
          - title: Rust
            url: https://doc.rust-lang.org/reference
            icon: si-rust
            color: "#000000"
          - title: Docker
            url: https://docs.docker.com/
            icon: si-docker
            color: "#2496ED"
        id: 5_892_documentation
