---
# tasks file for minecraft

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: iptables_allow_rules
  ansible.builtin.iptables:
      chain: INPUT
      protocol: "{{ item.protocol }}"
      source_port: "{{ item.port }}"
      destination_port: "{{ item.port }}"
      jump: ACCEPT
      comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 25565
      protocol: "tcp"
      comment: "minecraft server 001 TCP"
    - port: 25566
      protocol: "tcp"
      comment: "minecraft server 002 TCP"
    - port: 19132
      protocol: "udp"
      comment: "minecraft bedrock server"

- name: start_containers
  import_tasks: containers.yaml
