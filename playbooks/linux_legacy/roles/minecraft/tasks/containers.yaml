---
- name: start_mc001_container
  community.docker.docker_container:
    name: mc001
    image: itzg/minecraft-server
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    tty: true # docker run -t
    interactive: true # docker run -i
    ports:
      - 25565:25565
    volumes:
      - /mnt/nasa-p1z2-docker-data/minecraft/mc001/data:/data
    env:
      ALLOW_FLIGHT: "true"
      ALLOW_NETHER: "true"
      ENABLE_RCON: "true"
      ENABLE_ROLLING_LOGS: "true"
      EULA: "TRUE"
      GENERATE_STRUCTURES: "true"
      MAX_BUILD_HEIGHT: "256"
      MEMORY: "4G"
      PLAYER_IDLE_TIMEOUT: "10"
      PVP: "false"
      OPS: "krizzo"
      RCON_PASSWORD: "{{ mc_rcon_password }}"
      SPAWN_PROTECTION: "32"
      TZ: "America/Denver"
      SERVER_NAME: "mc001.krizzo.io"
      MOTD: "MC-001 Normal Server"
      # SEED: "2103424201258033754"
      SEED: "19851988201220152018"
- name: start_mc_bedrock_container
  community.docker.docker_container:
    name: mcbr
    image: itzg/minecraft-bedrock-server
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    tty: true
    interactive: true
    ports:
      - 19132:19132/udp
    volumes:
        - /mnt/nasa-p1z2-docker-data/minecraft/mcbr/data:/data
    env:
      EULA: "TRUE"
      VERSION: "LATEST"
      SERVER_NAME: "mcbr.krizzo.io"
      SERVER_PORT: "19132"
      GAMEMODE: "survival"
      DIFFICULTY: "normal"
      ALLOW_CHEATS: "true"
      MAX_PLAYERS: "15"
      ONLINE_MODE: "true"
      VIEW_DISTANCE: "32"
      TICK_DISTANCE: "6"
      PLAYER_IDLE_TIMEOUT: "10"
      LEVEL_NAME: "mcbr.krizzo.io"
      DEFAULT_PLAYER_PERMISSION_LEVEL: "member"
      ALLOW_LIST: "true"
      ALLOW_LIST_USERS: "krizzo,TRizzo4117,Necrolass4692,PenguinHedgehog,AlphaStrykeCTU,YellPikmin,SillyGirl33160"
      OPS: "2535470044274362"
      OP_PERMISSION_LEVEL: "4"

# - name: start_mc002_container
#   community.docker.docker_container:
#     name: mc002
#     image: itzg/minecraft-server
#     state: started
#     restart: yes
#     restart_policy: unless-stopped
#     pull: yes
#     tty: true         # docker run -t
#     interactive: true # docker run -i
#     ports:
#       - 25566:25565
#       - 8123:8123
#     volumes:
#       - /mnt/nasa-p1z2-docker-data-minecraft-mc002-data:/data
#     env:
#       ALLOW_FLIGHT: "true"
#       ALLOW_NETHER: "true"
#       ENABLE_RCON: "true"
#       ENABLE_ROLLING_LOGS: "true"
#       EULA: "TRUE"
#       GENERATE_STRUCTURES: "true"
#       MAX_BUILD_HEIGHT: "256"
#       PLAYER_IDLE_TIMEOUT: "10"
#       PVP: "false"
#       OPS: "krizzo"
#       RCON_PASSWORD: "RCON_PASSWD_CHANGE_ME"
#       SPAWN_PROTECTION: "32"
#       TZ: "America/Denver"
#       SERVER_NAME: "mc002.krizzo.io"
#       MOTD: "MC-003 Mod Server"
#       TYPE: "FORGE"
#       VERSION: "1.16.5"
#       FORGEVERSION: "36.1.0"
#       LEVEL_TYPE: "biomesoplenty"
#       MEMORY: "10G"
#       # JVM_OPTS: >-
#       #   -Xms10G
#       #   -Xmx10G
#       JVM_XX_OPTS: >-
#         -XX:+UseG1GC
#         -XX:+ParallelRefProcEnabled
#         -XX:MaxGCPauseMillis=200
#         -XX:+UnlockExperimentalVMOptions
#         -XX:+DisableExplicitGC
#         -XX:+AlwaysPreTouch
#         -XX:G1NewSizePercent=30
#         -XX:G1MaxNewSizePercent=40
#         -XX:G1HeapRegionSize=8M
#         -XX:G1ReservePercent=20
#         -XX:G1HeapWastePercent=5
#         -XX:G1MixedGCCountTarget=4
#         -XX:InitiatingHeapOccupancyPercent=15
#         -XX:G1MixedGCLiveThresholdPercent=90
#         -XX:G1RSetUpdatingPauseTimePercent=5
#         -XX:SurvivorRatio=32
#         -XX:+PerfDisableSharedMem
#         -XX:MaxTenuringThreshold=1
#       JVM_DD_OPTS: >-
#         -Dusing.aikars.flags=https://mcflags.emc.gs
#         -Daikars.new.flags=true
