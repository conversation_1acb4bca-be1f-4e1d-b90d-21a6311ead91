---

- name: Minecraft Instance 001
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/minecraft/mc001/data
    path: /mnt/nasa-p1z2-docker-data-minecraft-mc001-data
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Minecraft Instance 002
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/minecraft/mc002/data
    path: /mnt/nasa-p1z2-docker-data-minecraft-mc002-data
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Minecraft Bedrock Instance
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data
    path: /mnt/nasa-p1z2-docker-data
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs
