---
- name: start_jackett
  containers.podman.podman_container:
    name: jackett
    image: "binhex/arch-jackett"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - "9117:9117" # expose port for jackett webui
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/nasa-p1z2-docker-data/jackett/config:/config
      - /mnt/nasa-p1z2-data/torrents:/data
    env:
      UMASK: "{{ jackett_umask }}"
      PUID: "{{ jackett_puid }}"
      PGID: "{{ jackett_pgid }}"
