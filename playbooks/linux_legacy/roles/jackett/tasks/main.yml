---
# tasks file for jackett

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 9117
      protocol: "tcp"
      comment: "expose port for jackett webui"

- name: start_containers
  import_tasks: containers.yaml
