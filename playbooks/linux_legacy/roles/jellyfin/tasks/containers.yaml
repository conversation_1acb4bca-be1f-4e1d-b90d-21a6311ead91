---

- name: start_jellyfin_container
  community.docker.docker_container:
    name: jellyfin
    image: lscr.io/linuxserver/jellyfin
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - 8096:8096
      # - 8920:8920 #optional https webui, need to setup certs
      - 7359:7359/udp #optional
      # - 1900:1900/udp #optional
    volumes:
      - /mnt/nasa-p1z2-docker-data-jellyfin-config:/config
      - /mnt/nasa-p1z2-media-movies:/data/movies
      - /mnt/nasa-p1z2-media-series:/data/series
      - /mnt/nasa-p1z2-media-music:/data/music
    env:
      TZ: "America/Denver"
      JELLYFIN_PublishedServerUrl: "*************" #optional auto discovery response IP
      PUID: "1000"
      PUID: "1000"
