---
# tasks file for usenet

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: create_local_dirs_RHEL
  ansible.builtin.file:
    path: "{{item}}"
    state: directory
    mode: "0755"
    owner: "1000"
    group: "1000"
  loop:
    - "/mnt/local-data/usenet" # Local SSD storage so we can move fast
    - "/mnt/local-docker-data/nzbget/config"
    - "/mnt/local-docker-data/sonarr/config"
    - "/mnt/local-docker-data/radarr/config"
    - "/mnt/local-docker-data/nzbhydra2/config"
    - "/mnt/local-docker-data/overseerr/config"
    - "/mnt/local-docker-data/ombi/config"
    - "/mnt/local-docker-data/sabnzbd/config"
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 6789
      protocol: "tcp"
      comment: "expose port for nzbget ui http"
    - port: 8989
      protocol: "tcp"
      comment: "expose port for sonarr ui http"
    - port: 9897
      protocol: "tcp"
      comment: "expose port for sonarr ui https"
    - port: 7878
      protocol: "tcp"
      comment: "expose port for radarr ui http"
    - port: 5076
      protocol: "tcp"
      comment: "expose port for nzbhydra2 ui http"
    - port: 3579
      protocol: "tcp"
      comment: "expose port for ombi ui http"
    - port: 5055
      protocol: "tcp"
      comment: "expose port for overseerr ui http"
    - port: 8080
      protocol: "tcp"
      comment: "expose port for sabnzbd ui http"
    - port: 8090
      protocol: "tcp"
      comment: "expose port for sabnzbd ui https"
    # - port: 8118
    #   protocol: "tcp"
    #   comment: "expose port for privoxy ui PIA proxy"

- name: start_containers
  import_tasks: containers.yaml
