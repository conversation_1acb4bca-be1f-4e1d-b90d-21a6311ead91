---
# become: false can't be used right now running the container as rootless but need to figure out permission issues.

- name: pulling_latest_images
  containers.podman.podman_image:
    name: "{{ item }}"
  loop:
    - "binhex/arch-sonarr"
    - "binhex/arch-radarr"
    - "binhex/arch-nzbhydra2"
    - "binhex/arch-overseerr"
    - "lscr.io/linuxserver/ombi:latest"
    - "binhex/arch-sabnzbd"
  # - "binhex/arch-nzbget"
  # - binhex/arch-sabnzbdvpn

- name: stopping_containers
  containers.podman.podman_container:
    name: "{{ item }}"
    state: absent
  loop:
    - sonarr
    - radarr
    - nzbhydra2
    - overseerr
    - ombi
    - sabnzbd
  # - nzbget
  # - sabnzbdvpn

#MOVED
# NZB TV Series Search App
- name: start_sonarr_container
  containers.podman.podman_container:
    name: sonarr
    image: "binhex/arch-sonarr"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 8989:8989/tcp
      - 9897:9897/tcp
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/local-docker-data/sonarr/config:/config
      # - /mnt/nasa-p1z2-docker-data/sonarr/config:/config
      - /mnt/local-data/usenet:/data/usenet
      # - /mnt/nasa-p1z2-data/usenet:/data/usenet
      - /mnt/nasa-p1z2-data/media:/media
    env:
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"

#MOVED
# NZB Movie Search App
- name: start_radarr_container
  containers.podman.podman_container:
    name: radarr
    image: "binhex/arch-radarr"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 7878:7878/tcp
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/local-docker-data/radarr/config:/config
      # - /mnt/nasa-p1z2-docker-data/radarr/config:/config
      - /mnt/local-data/usenet:/data/usenet
      # - /mnt/nasa-p1z2-data/usenet:/data/usenet
      - /mnt/nasa-p1z2-data/media:/media
    env:
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"

# NZB Searcher
- name: start_nzbhydra2_container
  containers.podman.podman_container:
    name: nzbhydra2
    image: "binhex/arch-nzbhydra2"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 5076:5076/tcp
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/local-docker-data/nzbhydra2/config:/config
      # - /mnt/nasa-p1z2-docker-data/nzbhydra2/config:/config
      - /mnt/local-data/usenet:/data/usenet
      # - /mnt/nasa-p1z2-data/usenet:/data/usenet
    env:
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"

# Media Request System
- name: start_overseerr_container
  containers.podman.podman_container:
    name: overseerr
    image: "binhex/arch-overseerr"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 5055:5055/tcp
    volumes:
      - /mnt/local-docker-data/overseerr/config:/config
      # - /mnt/nasa-p1z2-docker-data/overseerr/config:/config
      - /etc/localtime:/etc/localtime:ro
    env:
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"

# Media Request System
- name: start_ombi_container
  containers.podman.podman_container:
    name: ombi
    image: "lscr.io/linuxserver/ombi:latest"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 3579:3579/tcp
    volumes:
      - /mnt/local-docker-data/ombi/config:/config
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
      BASE_URL: "/ombi" # optional

#MOVED
- name: start_sabnzbd_container
  containers.podman.podman_container:
    name: sabnzbd
    image: "binhex/arch-sabnzbd"
    state: started
    force_restart: yes
    restart_policy: always
    network: bridge
    ports:
      - "8080:8080" # expose port for sabnzbd webui http
      - "8090:8090" # expose port for sabnzbd webui https
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /mnt/local-docker-data/sabnzbd/config:/config
      - /mnt/local-data/usenet:/data/usenet
      # - /mnt/nasa-p1z2-data/usenet:/data/usenet
    env:
      DEBUG: "false"
      UMASK: "000"
      PUID: "1000"
      PGID: "1000"
#
# NZB Download App
# Default username:password nzbget/tegbzn6789
# - name: start_nzbget_container
#   containers.podman.podman_container:
#     name: nzbget
#     image: "binhex/arch-nzbget"
#     state: started
#     force_restart: yes
#     restart_policy: always
#     network: bridge
#     ports:
#       - 6789:6789/tcp
#     volumes:
#       - /etc/localtime:/etc/localtime:ro
#       - /mnt/local-docker-data/nzbget/config:/config
#       # - /mnt/nasa-p1z2-docker-data/nzbget/config:/config
#       - /mnt/local-data/usenet:/data/usenet
#       # - /mnt/nasa-p1z2-data/usenet:/data/usenet
#     env:
#       UMASK: "000"
#       PUID: "1000"
#       PGID: "1000"

# - name: start_sabnzbd_pia_vpn
#   containers.podman.podman_container:
#     name: sabnzbdvpn
#     image: binhex/arch-sabnzbdvpn
#     state: started
#     force_restart: yes
#     restart_policy: unless-stopped
#     pull: yes
#     sysctl: # Used for WireGuard Setup
#       net.ipv4.conf.all.src_valid_mark: 1
#     privileged: yes
#     # capabilities: # Used for openVPN setup
#     #   - NET_ADMIN
#     ports:
#       - "8080:8080" # expose port for sabnzbd webui http
#       - "8090:8090" # expose port for sabnzbd webui https
#       - "8118:8118" # expose port for privoxy  for other containers proxiy viah http://IP_ADDRESS:8118
#     volumes:
#       - /etc/localtime:/etc/localtime:ro
#       - /mnt/local-docker-data/sabnzbd/config:/config
#       - /mnt/nasa-p1z2-data/media:/data
#     env:
#       VPN_ENABLED: "yes"
#       VPN_USER: "{{ pia_account_username }}"
#       VPN_PASS: "{{ pia_account_password }}"
#       VPN_PROV: pia
#       VPN_CLIENT: wireguard
#       # VPN_CLIENT: openvpn
#       STRICT_PORT_FORWARD: "yes" # US locations not supported currently use ca-vancouver.privacy.network for WG setup
#       ENABLE_PRIVOXY: "yes"
#       LAN_NETWORK: ************/24
#       NAME_SERVERS: ************,************,*******,************,************,*******
#       DELUGE_DAEMON_LOG_LEVEL: info
#       DELUGE_WEB_LOG_LEVEL: info
#       # IMPORTANT 'VPN_INPUT_PORTS' is NOT to define the incoming port for the VPN,
#       # this environment variable is used to define port(s) you want to allow in to the VPN network when network binding multiple containers together,
#       # configuring this incorrectly with the VPN provider assigned incoming port COULD result in IP leakage, you have been warned!.
#       # VPN_INPUT_PORTS: 1234
#       # VPN_OUTPUT_PORTS: 5678
#       DEBUG: "false"
#       UMASK: "000"
#       PUID: "1000"
#       PGID: "1000"
