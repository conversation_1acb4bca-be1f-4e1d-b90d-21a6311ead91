---
# defaults file for pihole
provider:
  server: "{{ ansible_host }}"
  user: "{{ username }}"
  password: "{{ password }}"
  server_port: "{{ ansible_port }}"

my_user_name: "krizzo"
my_user_group: "krizzo"
piholeWebPass: "krizzo"

RHEL_install_packages:
- dnf-utils
#- podman # root-less volume mounting is fun....
#- podman-compose

# Ideally we could hav this be both formats
# docker format 53:53/tcp
# firewallds format 53/tcp
firewalld_ports:
- 53:53/tcp
- 53:53/udp
- 8053:80/tcp
