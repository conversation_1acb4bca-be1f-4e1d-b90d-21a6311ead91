---

- name: Start pi-hole container
  community.docker.docker_container:
    name: pihole
    image: "pihole/pihole:latest"
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - 53:53/tcp
      - 53:53/udp
      - 8053:80/tcp
    env:
      TZ: "America/Denver"
      WEBPASSWORD: "{{ piholeWebPass }}"
      ServerIP: "**************"
      PIHOLE_DNS_: "*******;*******"
    volumes:
      - /mnt/nasa-p1z2-docker-data-pihole-etc-pihole:/etc/pihole/
      - /mnt/nasa-p1z2-docker-data-pihole-etc-dnsmasqd:/etc/dnsmasq.d/
