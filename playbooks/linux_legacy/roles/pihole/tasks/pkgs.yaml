---
- name: RHEL Install list of packages
  ansible.builtin.dnf:
    pkg: "{{ RHEL_install_packages }}"
    state: present
  when: ansible_facts['os_family']|lower == 'rocky'
      or ansible_facts['os_family']|lower == 'centos'
      or ansible_facts['os_family']|lower == 'redhat'

- name: DEB Install list of packages
  ansible.builtin.dnf:
    pkg: "{{ DEB_install_packages }}"
    state: present
  when: ansible_facts['os_family']|lower == 'debian'
