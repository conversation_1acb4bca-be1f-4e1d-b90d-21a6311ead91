---
# tasks file for pihole

# RUN:
#   cd ~/repos/github.com/krizzo/ansible/playbooks/linux_plays
#   Base insatll:
#     ansible-playbook -l pihole -kK roles/rocky/init/main.yaml
#   pihole Install:
#     ansible-playbook -l pihole roles/pihole/tasks/main.yaml
#   Update Containers:
#     ansible-playbook -l pihole roles/pihole/tasks/main.yaml --start-at-task="Start containers"

- name: install_packages
  import_tasks: pkgs.yaml

- name: Import task nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: firewalld_settings
  import_tasks: firewalld.yaml

- name: Import task docker_setup
  import_tasks: docker_setup.yaml

- name: Import task containers
  import_tasks: containers.yaml
