---

- name: Mount PiHole Config NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/pihole/etc-pihole
    path: /mnt/nasa-p1z2-docker-data-pihole-etc-pihole
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs

- name: Mount PiHole Config NFS volume
  ansible.posix.mount:
    src: 192.168.28.31:/mnt/nasa-p1z2/docker-data/pihole/etc-dnsmasqd
    path: /mnt/nasa-p1z2-docker-data-pihole-etc-dnsmasq.d
    opts: rw,nolock,soft
    state: mounted
    fstype: nfs
