---
# tasks file for unifi-controller
- name: create_nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: iptables_allow_rules
  ansible.builtin.iptables:
    chain: INPUT
    protocol: "{{ item.protocol }}"
    source_port: "{{ item.port }}"
    destination_port: "{{ item.port }}"
    jump: ACCEPT
    comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 3478
      protocol: "udp"
      comment: "STUN"
    - port: 10001
      protocol: "udp"
      comment: "device discovery"
    - port: 8080
      protocol: "tcp"
      comment: "device and application communication"
    - port: 8443
      protocol: "tcp"
      comment: "GUI/API web browser"
    - port: 1900
      protocol: "udp"
      comment: "Discoverable on L2 network"
    - port: 8843
      protocol: "tcp"
      comment: "HTTPS redirect"
    - port: 8880
      protocol: "tcp"
      comment: "HTTP redirect"
    - port: 6789
      protocol: "tcp"
      comment: "UniFi Mobile Speed Test"
    - port: 5514
      protocol: "udp"
      comment: "Remote Syslog Capture"

- name: start_containers
  import_tasks: containers.yaml
