---
- name: create_unifi_config_dir
  ansible.builtin.file:
    path: "{{ unifi.config_path }}"
    state: directory
    recurse: true
    mode: "0755"
    owner: "{{ my_UID }}"
    group: "{{ my_GID }}"

- name: Start unifi-controller container
  community.docker.docker_container:
    name: unifi-controller
    image: lscr.io/linuxserver/unifi-controller
    state: started
    restart: yes
    restart_policy: unless-stopped
    ports:
      - 3478:3478/udp
      - 10001:10001/udp
      - 8080:8080
      - 8443:8443
      - 1900:1900/udp
      - 8843:8843
      - 8880:8880
      - 6789:6789
      - 5514:5514/udp
      # First 4 required remaining are optional
    volumes:
      - "{{ unifi.config_path }}:/config"
    env:
      PUID: "1000"
      PGID: "1000"
      #MEM_LIMIT: 1024M
      #MEM_STARTUP: 1024M
      TZ: "America/Denver"
