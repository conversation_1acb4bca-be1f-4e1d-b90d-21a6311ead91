# vim: filetype=sshconfig syntax=sshconfig

###
# Chicago Office ORD01

host as01-a12-ord01 as01-a12-ord01.ord01 as01-a12-ord01.ord01.corp.qops.net ********* ***********
  Hostname *********
  User admin
  UserKnownHostsFile ~/.ssh/networking/known_hosts
  ProxyCommand none
  ProxyJump none
  Pubkeyauthentication yes
  PasswordAuthentication no
  KbdInteractiveAuthentication yes
  ChallengeResponseAuthentication no
  TCPKeepAlive yes

host oob.as01-a12-ord01 oob.as01-a12-ord01.ord01 oob.as01-a12-ord01.ord01.corp.qops.net
  Hostname 0.0.0.0
  Port 3012
  ProxyCommand none
  ProxyJump none
  Pubkeyauthentication yes
  PasswordAuthentication no
  KbdInteractiveAuthentication yes
  ChallengeResponseAuthentication no
  TCPKeepAlive yes
