# vim: filetype=sshconfig syntax=sshconfig

###
# {{ cityName }} Office {{ officeSiteCode|upper }}

{% for switchID in switchList %}
host {{ switchID.switchName|lower }}-{{ officeSiteCode|lower}} {{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}} {{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}} {{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}}.{{ domainName }} {{ switchID.ipAddrMain }} {{ switchID.ipAddrSecondaries|join(" ") }}
  Hostname {{ switchID.ipAddrMain }}
  User {{ sshUserName }}
  UserKnownHostsFile {{ knownHostsPathAndFile }}
  ProxyCommand none
  ProxyJump none
  Pubkeyauthentication yes
  PasswordAuthentication no
  KbdInteractiveAuthentication yes
  ChallengeResponseAuthentication no
  TCPKeepAlive yes
  KexAlgorithms diffie-hellman-group-exchange-sha1,diffie-hellman-group14-sha1

host oob.{{ switchID.switchName|lower }}-{{ officeSiteCode|lower}} oob-{{ switchID.switchName|lower }}-{{ officeSiteCode|lower}} oob.{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}} oob-{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}} oob.{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}} oob-{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}} oob.{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}}.{{ domainName }} oob-{{ switchID.switchName|lower }}-{{ switchID.wingFloor }}-{{ officeSiteCode|lower}}.{{ officeSiteCode|lower}}.{{ domainName }}
  Hostname opengear.{{ officeSiteCode }}.{{ domainName }}
  User {{ opengearUserName }}
  Port {{ switchID.opengearPort }}
  ProxyCommand none
  ProxyJump none
  Pubkeyauthentication yes
  PasswordAuthentication no
  KbdInteractiveAuthentication yes
  ChallengeResponseAuthentication no
  TCPKeepAlive yes

{% endfor %}
