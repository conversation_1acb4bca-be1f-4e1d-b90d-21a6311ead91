---
# tasks file for ssh_config_gen

- name: create_config
  ansible.builtin.template:
    src: files/ssh.config.j2
    dest: "~/.ssh/{{ officeSiteCode }}_ansible_managed.config"


# - name: set_global_change_values
#   ansible.builtin.lineinfile:
#     path: "{{ ssh_config_file_location }}"
#     regexp: "{{ item.regex }}"
#     line: "{{ item.newline }}"
#     create: yes
#   loop: "{{ global_replace_values }}"

# - name: add_all_prod_opengears_to_ssh_config
#   community.general.ssh_config:
#     ssh_config_file: "{{ ssh_config_file_location }}"
#     host: "{{ item.hosts }}"
#     hostname: "{{ item.hostname }}"
#     # user: "krizzo"
#     # identity_file: "~/.ssh/id_ed25519"
#     # identity_file: "~/.ssh/id_rsa"
#     # user_known_hosts_file: "~/.ssh/krizzo_known_hosts"
#     # port: '22'
#     state: present
#   loop: "{{ prod_opengears }}"

# - name: add_all_stage_opengears_to_ssh_config
#   community.general.ssh_config:
#     ssh_config_file: "{{ ssh_config_file_location }}"
#     host: "{{ item.hosts }}"
#     hostname: "{{ item.hostname }}"
#     state: present
#   loop: "{{ stage_opengears }}"

# - name: add_all_corp_opengears_to_ssh_config
#   community.general.ssh_config:
#     ssh_config_file: "{{ ssh_config_file_location }}"
#     host: "{{ item.hosts }}"
#     hostname: "{{ item.hostname }}"
#     state: present
#   loop: "{{ corp_opengears }}"

# - name: add_all_oob_firewalls_to_ssh_config
#   community.general.ssh_config:
#     ssh_config_file: "{{ ssh_config_file_location }}"
#     host: "{{ item.hosts }}"
#     hostname: "{{ item.hostname }}"
#     port: "{{ item.serial_port }}"
#     state: present
#   loop: "{{ fw_oob_ports }}"
