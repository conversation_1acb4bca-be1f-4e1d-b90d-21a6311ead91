anidb:
  language: en
  password:
  username:
libraries:
{% for libraryName in pmm.plex_libraries %}
  {{ libraryName }}:
    metadata_path:
      - folder: config/{{ libraryName | replace(" ", "_") }}/
{% endfor %}
mal:
  authorization:
    access_token:
    expires_in:
    refresh_token:
    token_type:
  client_id:
  client_secret:
mdblist:
  apikey:
  cache_expiration: 60
notifiarr:
  apikey:
omdb:
  apikey:
  cache_expiration: 60
playlist_files:
- file: config/playlists.yml
plex:
  clean_bundles: false
  empty_trash: false
  optimize: false
  timeout: 60
  token: {{ pmm.plex.token }}
  url: {{ pmm.plex.url }}
radarr:
  add_existing: false
  add_missing: false
  availability: announced
  monitor: true
  plex_path:
  quality_profile: {{ pmm.radarr.quality_profile }}
  radarr_path:
  root_folder_path: {{ pmm.radarr.root_folder }}
  search: false
  tag:
  token: {{ pmm.radarr.token }}
  url: {{ pmm.radarr.url }}
  upgrade_existing: false
settings:
  asset_depth: 0
  asset_directory:
{% for libraryName in pmm.plex_libraries %}
    - config/{{ libraryName | replace(" ", "_") }}/assets
{% endfor %}
  asset_folders: true
  cache: true
  cache_expiration: 60
  check_nightly: false
  create_asset_folders: true
  custom_repo:
  default_collection_order:
  delete_below_minimum: true
  delete_not_scheduled: false
  dimensional_asset_rename: false
  download_url_assets: true
  ignore_ids:
  ignore_imdb_ids:
  item_refresh_delay: 0
  minimum_items: 1
  missing_only_released: false
  only_filter_missing: false
  playlist_report: false
  playlist_sync_to_user: all
  prioritize_assets: false
  run_again_delay: 2
  save_report: false
  show_asset_not_needed: true
  show_filtered: false
  show_missing: true
  show_missing_assets: true
  show_missing_episode_assets: false
  show_missing_season_assets: false
  show_options: false
  show_unmanaged: true
  sync_mode: append
  tvdb_language: eng
  verify_ssl: true
{# sonarr:
  add_existing: false
  add_missing: false
  cutoff_search: false
  language_profile: English
  monitor: all
  plex_path:
  quality_profile: {{ pmm.sonarr.quality_profile }}
  root_folder_path: {{ pmm.sonarr.root_folder }}
  search: false
  season_folder: true
  series_type: standard
  sonarr_path:
  tag:
  token: {{ pmm.sonarr.token }}
  url: {{ pmm.sonarr.url }}
  upgrade_existing: false #}
tautulli:
  apikey: {{ pmm.tautulli.apikey }}
  url: {{ pmm.tautulli.url }}
tmdb:
  apikey: {{ pmm.tmdb.apikey }}
  language: en
  cache_expiration: 60
  region:
## Trakt PIN is a one time use, Click Authorize under https://trakt.tv/oauth/applications/99201 if trakt is failing.
trakt: ## client id + secret + pin will fill out the authorization section on first run. After that copy the auth values from the config.yml file
  authorization:
    access_token: {{ pmm.trakt.authorization.access_token }}
    created_at: {{ pmm.trakt.authorization.created_at }}
    expires_in: {{ pmm.trakt.authorization.expires_in }}
    refresh_token: {{ pmm.trakt.authorization.refresh_token }}
    scope: public
    token_type: {{ pmm.trakt.authorization.token_type }}
  client_id: {{ pmm.trakt.client_id }}
  client_secret: {{ pmm.trakt.client_secret }}
  pin: {{ pmm.trakt.onetimeuse_pin }}
webhooks:
  changes:
  error:
  run_end:
  run_start:
  version:
