---
- name: pulling_latest_images_RHEL
  containers.podman.podman_image:
    name: "{{ item }}"
  loop:
    - "meisnate12/plex-meta-manager"

- name: stopping_containers_RHEL
  containers.podman.podman_container:
    name: "{{ item }}"
    state: absent
  loop:
    - pmm

# # Run once setup
# # podman run --rm -it --name pmm -e TZ="America/Denver" -v "/mnt/nasa-p1z2-docker-data/plex-meta-manager/config:/config:rw" meisnate12/plex-meta-manager --run
# - name: start_plex_meta_manager_container_RHEL
#   become: false
#   containers.podman.podman_container:
#     name: pmm
#     image: meisnate12/plex-meta-manager
#     state: started
#     # restart: yes
#     # restart_policy: always
#     rm: yes
#     command: "--run"
#     volumes:
#       - "{{ pmm.config_path }}:/config:rw"
#     env:
#       TZ: "America/Denver"
#       # PMM_DELETE_COLLECTIONS: True # Delete all collections and rebuild the ones managed via PMM

# Run as a service
# podman run --rm -d --name pmm -e TZ="America/Denver" -e PMM_TIME="02:00" -v "/mnt/nasa-p1z2-docker-data/plex-meta-manager/config:/config:rw" meisnate12/plex-meta-manager
- name: start_plex_meta_manager_container_RHEL
  become: false
  containers.podman.podman_container:
    name: pmm
    image: meisnate12/plex-meta-manager
    state: started
    restart: yes
    restart_policy: always
    volumes:
      - "{{ pmm.config_path }}:/config:rw"
    env:
      TZ: "America/Denver"
      PMM_TIME: 02:00 # Run at 02:00 system time
      # PMM_DELETE_COLLECTIONS: True # Delete all collections and rebuild the ones managed via PMM
