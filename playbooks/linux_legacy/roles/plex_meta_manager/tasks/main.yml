---
# tasks file for plex_meta_manager

- name: nfs_mounts
  import_tasks: nfs_mounts.yaml

- name: create_plex_meta_manager_config_directories
  ansible.builtin.file:
    path: "{{ pmm.config_path }}/{{item.path | replace(' ','_') }}"
    state: directory
    recurse: true
    mode: "0755"
    owner: "{{ my_UID }}"
    group: "{{ my_GID }}"
  with_community.general.filetree: "plex-meta-manager/"
  when: item.state == 'directory'

- name: copy_plex_meta_manager_configs
  ansible.builtin.copy:
    src: "{{item.src}}"
    dest: "{{ pmm.config_path }}/{{item.path | replace(' ','_') }}"
    mode: "0644"
    owner: "{{ my_UID }}"
    group: "{{ my_GID }}"
  with_community.general.filetree: "plex-meta-manager/"
  when: item.state == 'file'

- name: backup_plex_meta_manager_config
  ansible.builtin.copy:
    src: "{{ pmm.config_path }}/config.yml"
    dest: "{{ pmm.config_path }}/config.yml.bak-{{ ansible_date_time.iso8601_basic_short}}"

- name: write_plex_meta_manager_config_from_template
  ansible.builtin.template:
    src: templates/plex-meta-manager/config.yml.j2
    dest: "{{ pmm.config_path }}/config.yml"

- name: start_containers_RHEL
  import_tasks: containers_RHEL.yaml
  when: ansible_facts['os_family']|lower == 'rocky'
    or ansible_facts['os_family']|lower == 'centos'
    or ansible_facts['os_family']|lower == 'redhat'
