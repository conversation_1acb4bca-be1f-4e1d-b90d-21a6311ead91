# To Find lists google search https://www.google.com/search?q=MOVIE_NAME+"animated+film+series"+site%3Aimdb.com%2Flist
templates:
  Franchise:
    url_poster: <<poster>>
    sort_title: ++++_<<collection_name>>
    collection_order: release
    delete_not_scheduled: true
    run_again: true
    visible_home: true
    visible_shared: true
    sync_mode: sync
collections:
  Marvel Cinematic Universe:
    template:
      { name: <PERSON>an<PERSON><PERSON>, poster: https://theposterdb.com/api/assets/162885 }
    sort_title: MCU
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls039269245/
    summary: An American media franchise and shared fictional universe that is centered on a series of superhero films, independently produced by Marvel Studios and based on characters that appear in publications by Marvel Comics. The franchise has expanded to include comic books, short films, and television series.
    # radarr_add_missing: true # Be careful with enabling this setting on a given collection it could get a lot of extra movies you may not want.

  Disney Animated Films:
    template:
      { name: <PERSON>an<PERSON><PERSON>, poster: https://theposterdb.com/api/assets/11782 }
    sort_title: ++++_DisneyAnimatedFilms
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls059383351/
      - https://www.imdb.com/list/ls076436131/
      - https://www.imdb.com/list/ls024560153/
      - https://www.imdb.com/list/ls026880292/
    summary: Disney Animated Films
    # radarr_add_missing: true # Be careful with enabling this setting on a given collection it could get a lot of extra movies you may not want.

  Disney Live Action Films:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/105622 }
    sort_title: ++++_DisneyLiveActionFilms
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls062279726/
    summary: Disney Live Action Films
    radarr_add_missing: true # Be careful with enabling this setting on a given collection it could get a lot of extra movies you may not want.

  Fast and Furious:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/164783 }
    sort_title: +++_Fast
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068935667/
    summary: The Fast and the Furious is an American media franchise centered on a series of action films that is largely concerned with illegal street racing, heists and spies. The franchise also includes short films, a television series, live shows, and theme park attractions.

  Pirates of the Caribbean:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/49933 }
    sort_title: ++++_Pirates
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls023657263/
    summary: A series of fantasy swashbuckler films based on Walt Disney's theme park ride of the same name. The films follow the adventures of Captain Jack Sparrow and take place in a fictional historical setting; a world ruled by the British Empire, the East India Trading Company and the Spanish Empire, with pirates representing freedom from the ruling powers.

  Star Wars:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/107867 }
    sort_title: ++++_StarWars
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls046295261/
    summary: Star Wars is an American epic space-opera multimedia franchise created by George Lucas, which began with the eponymous 1977 film and quickly became a worldwide pop-culture phenomenon.

  #  Chronological Order
  Star Wars Chronological Order:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/39679 }
    sort_title: ++++_StarWarsChronologicalOrder
    collection_order: custom
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068055646/
    summary: Star Wars is an American epic space-opera multimedia franchise created by George Lucas, which began with the eponymous 1977 film and quickly became a worldwide pop-culture phenomenon.

  The Wizarding World:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/166278 }
    sort_title: ++++_Wizarding
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls060991914/
    summary: The Wizarding World is a fantasy media franchise and shared fictional universe centred on a series of films, based on the Harry Potter novel series by J. K. Rowling.
    # label: Family-Friendly,Kids

  Back to the Future:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/87588 }
    sort_title: +++_Back
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls093597379
    summary: Back to the Future is an American science fiction comedy franchise created by Robert Zemeckis and Bob Gale. The franchise follows the adventures of a high school student, Marty McFly, and an eccentric scientist, Dr. Emmett "Doc" Brown, as they use a DeLorean time machine to time travel to different periods in the history of the fictional town of Hill Valley, California.

  Indiana Jones:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/105678 }
    sort_title: +++_Indiana
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls027779939
    summary: The Adventure films series from the Director George Lucas and Steven Spielberg, starring Harrison Ford as the archaeologist Dr. Henry Walton "Indiana" Jones.

  Marvel:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/125372 }
    sort_title: +++_Marvel
    # trakt_list:
    #   - https://trakt.tv/users/isis-jansen/lists/marvel?sort=rank,desc
    imdb_list:
      - https://www.imdb.com/list/ls000024621
    summary: Marvel Comics is the brand name and primary imprint of Marvel Worldwide Inc., formerly Marvel Publishing, Inc. and Marvel Comics Group, a publisher of American comic books and related media. In 2009, The Walt Disney Company acquired Marvel Entertainment, Marvel Worldwide's parent company.

  Middle Earth:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/191469 }
    sort_title: ++++_MiddleEarth
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls055713151
    summary: The Lord of the Rings is an epic high-fantasy novel by English author and scholar J. R. R. Tolkien. Set in Middle-earth, intended to be Earth at some distant time in the past, the story began as a sequel to Tolkien's 1937 children's book The Hobbit, but eventually developed into a much larger work.

  # The Hobbit
  The Hobbit:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/191470 }
    sort_title: +++_Hobbit
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls069544617/
    summary: The Hobbit

  # Lord of the Rings
  The Lord of the Rings:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/191471 }
    sort_title: ++++_LordoftheRings
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls072068350/
    summary: The Lord of the Rings

  Bourne:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/16464 }
    sort_title: +++_Bourne
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls026772377/
    summary: The Bourne franchise consists of action-thriller installments based on the character Jason Bourne, created by author Robert Ludlum. The franchise includes five theatrical films, and a spin-off prequel television series.

  X-Men:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/143883 }
    sort_title: ++_XMEN
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls026465600/
    summary: X-Men is an American superhero film series based on the fictional superhero team of the same name, who originally appeared in a series of comic books created by Stan Lee and Jack Kirby and published by Marvel Comics.

  Batman:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/142579 }
    sort_title: ++_Batman
    # trakt_list:
    #   - https://trakt.tv/users/maz/lists/batman-anthology-1989-1997?sort=rank,asc
    imdb_list:
     - https://www.imdb.com/list/ls051841763/
    summary: Batman is a superhero appearing in American comic books published by DC Comics.

  The Dark Knight:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/141711 }
    sort_title: ++_BatmanDarkKnight
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls047442692/
    summary: Batman is a superhero appearing in American comic books published by DC Comics.

  James Bond:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/201230 }
    sort_title: +++_JamesBond
    # trakt_list:
    #   - https://trakt.tv/users/rd1701/lists/bond?sort=released,asc
    imdb_list:
     - https://www.imdb.com/list/ls006405458/
    summary: The James Bond film series is a British series of spy films based on the fictional character of MI6 agent James Bond, codename "007". With all of the action, adventure, gadgetry & film scores that Bond is famous for.

  DC Extended Universe:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/128644 }
    sort_title: +_DCEU
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls062501894/
    summary: The DC Extended Universe (DCEU) is an American media franchise and shared universe centered on a series of superhero films and television series produced by DC Films and distributed by Warner Bros. Pictures.

  Transformers:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/14175 }
    sort_title: ++_Transformers
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls033452628/
    summary: The Transformers series follows the continuing battle between the Autobots and the Decepticons and ultimately, the triumph of good over evil. This collection includes the Theatrically Released Films of the transformers saga only.

  Hunger Games:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/31012 }
    sort_title: +_HungerGames
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls069064647/
    summary: A science fiction film series based on the novel of the same name by Suzanne Collins. The film series takes place in a dystopian post-apocalyptic future in the nation of Panem, featuring the protagonist, Katniss Everdeen.

  Divergent:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/53782 }
    sort_title: +_Divergent
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls079078568/
    summary: The series consists of three science fiction action films set in a dystopian society.

  Maze Runner:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/174 }
    sort_title: +_MazeRunner
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls020767998/
    summary: Maze Runner is a North American film trilogy, consisting of science-fiction dystopian action adventure films based on The Maze Runner novels by the North American author James Dashner.

  Alien:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/94295 }
    sort_title: +_Alien
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls009085811/
    summary: A science-fiction horror and action media franchise centered on the film series which depicts warrant officer Ellen Ripley (Sigourney Weaver) and her battles with an extraterrestrial lifeform, commonly referred to as "the Alien" or Xenomorph.

  Predator:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/94456 }
    sort_title: +_Predator
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls026316908/
    summary: Humankind's encounters with an intelligent race of extraterrestrial trophy-seeking, DNA engineering military personnel known as the "Predator".

  Alien vs Predator:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/94302 }
    sort_title: +_AVP
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls076895398/ #  Chronological Order
    summary: Alien vs Predator

  3 Ninjas:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/16411 }
    sort_title: +_ThreeNinjas
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls043697624/
    summary: 3 Ninjas

  Teenage Mutant Ninja Turtles:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/122012 }
    sort_title: +++_TMNT
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls029535241/
    summary: Teenage Mutant Ninja Turtles

  Jurassic Park:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/158968 }
    sort_title: +++_JurassicPark
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068935685/
    summary: Jurassic Park

  The Karate Kid:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/163394 }
    sort_title: +_KarateKid
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls000683693/
    summary: The Karate Kid

  Oceans Series:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/164814 }
    sort_title: +_OceansSeries
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068557644/
    summary: Oceans Series

  John Wick:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/181 }
    sort_title: ++_JohnWick
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls044102039/
    summary: John Wick

  Mission Impossible:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/11546 }
    sort_title: +_MissionImpossible
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls066600640/
    summary: Mission Impossible

  Ernest Series:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/24572 }
    sort_title: +_ErnestSeries
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls007691427/
    summary: Ernest Series

  Taken:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/112816 }
    sort_title: +_Taken
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls047442581/
    summary: Taken

  Terminator:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/42980 }
    sort_title: ++_Terminator
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068935243/
    summary: Terminator

  Tron:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/5304 }
    sort_title: +_Tron
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls023985613/
    summary: Tron

  Tremors:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/107247 }
    sort_title: +_Tremors
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls093337873/
    summary: Tremors

  Army of the Dead:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/205933 }
    sort_title: +_ArmyoftheDead
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls088950897/
    summary: Army of the Dead

  The Mighty Ducks:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/192594 }
    sort_title: +++_MightyDucks
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls088108219/
    summary: The Mighty Ducks

  Despicable Me and Minions:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/122830 }
    sort_title: +++_DespicableMeAndMinions
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068935616/
    summary: Despicable Me and Minions

  Despicable Me:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/22035 }
    sort_title: ++_DespicableMe
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls041137119/
    summary: Despicable Me

  Minions:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/27388 }
    sort_title: ++_Minions
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls041137727/
    summary: Minions

  Home Alone:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/23878 }
    sort_title: +++_HomeAlone
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls047446226/
    summary: Home Alone

  How to Train Your Dragon:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/95986 }
    sort_title: +++_HowtoTrainYourDragon
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls043676648/
    summary: How to Train Your Dragon

  LEGO Movies:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/46059 }
    sort_title: +++_LEGOMovies
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068935205/
    summary: LEGO Movies

  Honey I Shrunk the Kids:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/27434 }
    sort_title: +++_HoneyIShrunktheKids
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls043245324/
    summary: Honey I Shrunk the Kids

  Pixar Films:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/456 }
    sort_title: ++++_PixarFilms
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls076436404/
    summary: Pixar Films
    radarr_add_missing: true # Be careful with enabling this setting on a given collection it could get a lot of extra movies you may not want.

  Shrek:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/57044 }
    sort_title: +++_Shrek
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls041070883/
    summary: Shrek

  The Transporter:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/69471 }
    sort_title: ++_TheTransporter
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls053513133/
    summary: The Transporter

  Alvin and the Chipmunks:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/23071 }
    sort_title: +++_AlvinandtheChipmunks
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls022790876/
    summary: Alvin and the Chipmunks

  Die Hard:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/719 }
    sort_title: ++_DieHard
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls057313912/
    summary: Die Hard

  Ghostbusters:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/203163 }
    sort_title: ++_Ghostbusters
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls082530214/
    summary: Ghostbusters

  Narnia:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/150 }
    sort_title: ++_Narnia
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls069668272/
    summary: Narnia

  Matrix:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/147306 }
    sort_title: ++_Matrix
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls041070228/
    summary: Matrix

  Men in Black:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/161673 }
    sort_title: ++_MIB
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls567043634/
    summary: Men in Black

  Sonic:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/237758 }
    sort_title: +++_Sonic
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls092781575/
    summary: Sonic

  Hitchcock:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/24977 }
    sort_title: ++_Hitchcock
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls003046818/
    summary: Hitchcock

  DC Comics:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/128644 }
    sort_title: +_DCComics
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls000024643/
    summary: DC Comics

  Superman:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/147059 }
    sort_title: +_Superman
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls068942825/
    summary: Superman

  Wolverine:
    template:
      { name: Franchise, poster: https://theposterdb.com/api/assets/143893 }
    sort_title: +_Wolverine
    sync_mode: sync
    imdb_list:
      - https://www.imdb.com/list/ls064519490/
    summary: Wolverine

  # TEMPLATE:
  #   template:
  #     {
  #       name: Franchise,
  #       poster: https://theposterdb.com/api/assets/999999999999999999,
  #     }
  #   sort_title: TEMPLATE
  #   sync_mode: sync
  #   imdb_list:
  #     - https://www.imdb.com/list/ls9999999999999999/
  #   summary: TEMPLATE
