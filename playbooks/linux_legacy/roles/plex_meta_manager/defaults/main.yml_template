---
# Copy this file to main.yml and edit the values.
pmm:
  config_path: /mnt/nasa-p1z2-docker-data/plex-meta-manager/config
  my_GID: GROUPNAME_OR_GID
  my_UID: USERNAME_OR_UID
  plex:
    token: null
    url: http://SERVER_IP:32400
  plex_libraries:
    - Movies
    - TV Shows
  radarr:
    token: null
    url: http://SERVER_IP:7878
  sonarr:
    token: null
    url: http://SERVER_IP:8989
  tautulli:
    apikey: null
    url: http://SERVER_IP:8181
  tmdb:
    apikey: null
  trakt:
    authorization:
      access_token: null
      created_at: null
      expires_in: null
      refresh_token: null
      scope: public
      token_type: null
    client_id: null
    client_secret: null
    onetimeuse_pin: null
provider:
  password: "{{ password }}"
  server: "{{ ansible_host }}"
  server_port: "{{ ansible_port }}"
  user: "{{ username }}"
