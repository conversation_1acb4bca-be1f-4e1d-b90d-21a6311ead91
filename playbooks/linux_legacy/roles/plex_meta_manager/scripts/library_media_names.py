#!/usr/bin/env python
# -*- coding: utf-8 -*-

'''
Write each library's media names to a library list.
'''

from os import listdir, chmod
from os.path import isfile, join
import re

from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from plexapi.server import PlexServer, CONFIG
import yaml
from deepdiff import DeepDiff
from datetime import datetime

from pprint import pprint as pp
from time import sleep

def write_library_list_file_from_plex(library=None, MyPlexServer=None):
    library_filename = library.lower().replace(' ', '_')
    pmf = open(f"plex_{library_filename}_list.yml", 'w')
    pmf.write("---\nmetadata:\n")
    for media in MyPlexServer.library.section(library).all():
        scrubbed_name = re.sub('[^a-zA-Z0-9_ ]+', '', media.title)
        pretty_name = f'{scrubbed_name} ({media.year})'
        true_name = media.title
        pmf.write(f'  {pretty_name}:\n    title: "{true_name}"\n    year: {media.year}\n    url_poster: https://theposterdb.com/api/assets/\n')
    pmf.close()


# Function decommissioned
def local_poster_file_upload_fuzzy_matching(library=None, min_ratio_match=78, MyPlexServer=None):
    """
        78% appears to be a good value for updating a poster correctly matching it's name to the media name from plex.
        https://python-plexapi.readthedocs.io/en/latest/modules/mixins.html#plexapi.mixins.PosterMixin
    """
    try:
        ratio_val_max = 100 # Exclusive
        range_count = 0
        lowest_ratio = 100
        year_tolerance = 2 # The movie date can be +- one year
        library_filename = library.lower().replace(' ', '_')

        posters_files = get_posters_files_of_library(library=library)
        if posters_files == None:
            return None

        pmf = open(f"plex_{library_filename}_list.yml", 'w')
        pmf.write("---\nmetadata:\n")

        for media in MyPlexServer.library.section(library).all():
            scrubbed_name = re.sub('[^a-zA-Z0-9_ ]+', '', media.title)
            pretty_name = f'{scrubbed_name} ({media.year})'
            true_name = media.title
            poster_name = process.extractOne(pretty_name, posters_files.keys(), scorer=fuzz.token_sort_ratio)
            tolerance_list =  [ str(r) for r in range(media.year - year_tolerance, media.year + year_tolerance + 1) ]
            if poster_name[1] >= min_ratio_match:
                if str(media.year) in poster_name[0]: # Strong match with the name fuzzy and year
                    pmf.write(f'  {pretty_name}:\n    title: "{true_name}"\n    year: {media.year}\n    file_poster: ./assets/posters/{poster_name[0]} # {poster_name[1]}\n')
                elif bool([e for e in tolerance_list if(e in poster_name[0])]): # Year is off but still a strong match
                    pmf.write(f'  {pretty_name}:\n    title: "{true_name}"\n    year: {media.year}\n    file_poster: ./assets/posters/{poster_name[0]} # movie year does not match file year, fuzzy ratio {poster_name[1]}\n')
                else: # There is a match but the years don't match up
                    pmf.write(f'  {pretty_name}:\n    title: "{true_name}"\n    year: {media.year}\n    file_poster:  ./assets/posters/null_poster.png # No poster match with correct year, name is close to {poster_name[0]} with ratio {poster_name[1]}\n')
            else:
                pmf.write(f'  # "{pretty_name}" - "{true_name}" - "{media.year}" No poster match with ratio {poster_name[1]}\n')
            if poster_name[1] in range(min_ratio_match, (ratio_val_max + 1)):
                range_count += 1
                if poster_name[1] < lowest_ratio: lowest_ratio = poster_name[1]
            if poster_name[1] < min_ratio_match:
                print(f"--- DEBUG - Ratio {poster_name[1]} match low for move '{pretty_name}' poster '{poster_name[0]}' : '{posters_files[poster_name[0]]}'")
        pmf.close()
        print(f"--- DEBUG - {range_count} identified posters for {library} between {min_ratio_match} - {ratio_val_max} with lowest ratio of {lowest_ratio}")
    except NoneType as e:
        print(f"!!! WARN  - No match found.\n{e}")
    except Exception as e:
        print(f"!!! ERROR - Exception found.\n{e}")

# Function decommissioned
def get_local_posters_files_of_library(library=None):
    """
    Take a library directy name and return a list of all files in the library
    Expecting a dir structure as './libraries/<library_name_lowercase>/posters/'
    """
    posters_path = f"../files/plex-meta-manager/{library.replace(' ', '_')}/assets/posters/"
    try:
        return {f: join(posters_path, f) for f in listdir(posters_path) if isfile(join(posters_path, f))}
    except FileNotFoundError as e:
        print(f"!!! WARN  - Expected dir '{posters_path}' is missing for library '{library}'.")
        return None


def read_metadata_yaml_file(yaml_file):
    try:
        current_file_data = None
        with open(yaml_file, 'r') as fd:
            current_file_data = yaml.safe_load(fd)
        return current_file_data
    except Exception as e:
        print(f"!!! ERROR !!! read_metadata_yaml_file: {e}")

def write_metadata_yaml_file(yaml_file, data=None):
    try:
        # current_time = datetime.now().strftime("%Y%m%dT%H%M%SZ")  # iso8601 basic short
        # data['_time'] = current_time
        with open(yaml_file, 'w') as fd:
            yaml.safe_dump(data, fd)
        chmod(yaml_file, 0o664)
    except Exception as e:
        print(f"!!! ERROR !!! write_metadata_yaml_file: {e}")


def write_dict_movies_to_txt_list(filename, data):
    try:
        # current_time = datetime.now().strftime("%Y%m%dT%H%M%SZ")  # iso8601 basic short
        # data['_time'] = current_time
        with open(filename, 'w') as fd:
            for m in sorted(data['metadata'].keys()):
                fd.write(f"{m}\n")
        chmod(filename, 0o664)
    except Exception as e:
        print(f"!!! ERROR !!! write_dict_movies_to_txt_list: {e}")


def generate_library_metadata(library=None, MyPlexServer=None):
    try:
        library_clean_name = library.replace(' ', "_")
        library_media = {'metadata': None}
        media_dict = dict()

        for media in MyPlexServer.library.section(library).all():
            cleaned_title = media.title.replace('&', 'and')
            cleaned_title = re.sub('[^a-zA-Z0-9_ ]+', '', cleaned_title) # Replace any special characters with nothing
            cleaned_title = re.sub('\s+', ' ', cleaned_title) # Clean up any multiple spaces with a single space

            pretty_name = f"{cleaned_title} ({media.year})"
            media_data = {'title': media.title, 'year': media.year, 'url_poster': 'https://theposterdb.com/api/assets/'}
            media_dict = media_dict | {pretty_name: media_data}
        library_media['metadata'] = media_dict
        return library_media
    except Exception as e:
        print(f"!!! ERROR !!! GENERATE Function: {e}")


def main():
    plex_url = CONFIG.data['auth'].get('server_baseurl')
    plex_token = CONFIG.data['auth'].get('server_token')
    MyPlexServer = PlexServer(plex_url, plex_token)

    for library in MyPlexServer.library.sections():
        library_clean_name = library.title.replace(' ', '_')
        if library.title == 'Movies':
            plex_library_data = generate_library_metadata(library.title, MyPlexServer)
            # yaml_filename = f"{library_clean_name}_plex_metadata.yml"
            # write_metadata_yaml_file(yaml_filename, plex_library_data)
            # txt_filename = yaml_filename.replace('yml', 'txt')
            # write_dict_movies_to_txt_list(txt_filename, plex_library_data)

            # yaml_filename = f"{library_clean_name}_pmm_metadata.yml"
            yaml_filename = f"../files/plex-meta-manager/{library_clean_name}/Metadata.yml"
            pmm_library_data = read_metadata_yaml_file(yaml_filename)
            # txt_filename = yaml_filename.replace('yml', 'txt')
            # write_dict_movies_to_txt_list(txt_filename, pmm_library_data)

            missing_media_from_pmm_metadata = dict()
            missing_media_from_pmm_metadata['metadata'] = {i:j for i,j in plex_library_data['metadata'].items() if i not in pmm_library_data['metadata']}
            yaml_filename = f"{library_clean_name}_missing_metadata.yml"
            write_metadata_yaml_file(yaml_filename, missing_media_from_pmm_metadata)
            # txt_filename = yaml_filename.replace('yml', 'txt')
            # write_dict_movies_to_txt_list(txt_filename, missing_media_from_pmm_metadata)

            print(f"Plex '{library.title}' media: {len(plex_library_data['metadata'])}\nPMM media: {len(pmm_library_data['metadata'])}\nPMM missing media: {len(missing_media_from_pmm_metadata['metadata'])}")

            # write_library_list_file_from_plex(library.title, MyPlexServer)


if __name__ == "__main__":
    main()
