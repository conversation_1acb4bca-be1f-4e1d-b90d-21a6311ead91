---

- name: set_file_owner
  set_fact:
      dhcpd_owner: "{{ 'root' if ansible_facts['os_family']|lower == 'debian' else 'dhcpd' }}"

- name: dhcpd_service_user
  debug: msg="{{ dhcpd_owner }}"

- name: set_file_group
  set_fact:
      dhcpd_group: "{{ 'root' if ansible_facts['os_family']|lower == 'debian' else 'dhcpd' }}"

- name: dhcpd_service_user
  debug: msg="{{ dhcpd_group }}"

- name: create_dhcpd_directories
  ansible.builtin.file:
    path: "{{ dhcpd_conf_dst }}{{item.path}}"
    state: directory
    mode:  '0755'
    owner: "{{ dhcpd_owner }}"
    group: "{{ dhcpd_group }}"
  with_filetree: "{{ dhcp_confg_src }}"
  when:
    - item.state == 'directory'
    - item.path is not match("vagrant*")

- name: copy_shared_conf_files
  ansible.builtin.copy:
    src: "{{item.src}}"
    dest: "{{ dhcpd_conf_dst }}{{item.path}}"
    mode:  '0644'
    owner: "{{ dhcpd_owner }}"
    group: "{{ dhcpd_group }}"
  with_filetree: "{{ dhcp_confg_src }}"
  when:
    - item.state == 'file'
    - item.path is not match("dhcpd_*")
        # Avoid copying the dhcpd.conf it will be different between the two hosts for HA
  notify:
    - restart_dhcpd_RHEL
    - restart_isc-dhcp-server_DEB

- name: copy_primary_dhcpd_conf_file
  ansible.builtin.copy:
    src: "{{ dhcp_confg_src }}{{ primary_dhcpd_conf }}"
    dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
    mode:  '0644'
    owner: "{{ dhcpd_owner }}"
    group: "{{ dhcpd_group }}"
  when: (ansible_facts['hostname']|lower == "silmarillion-001")
  notify:
    - restart_dhcpd_RHEL
    - restart_isc-dhcp-server_DEB

- name: copy_secondary_dhcpd_conf_file
  ansible.builtin.copy:
    src: "{{ dhcp_confg_src }}{{ secondary_dhcpd_conf }}"
    dest: "{{ dhcpd_conf_dst }}{{ dhcpd_conf_filename }}"
    mode:  '0644'
    owner: "{{ dhcpd_owner }}"
    group: "{{ dhcpd_group }}"
  when: (ansible_facts['hostname']|lower == "silmarillion-002")
  notify:
    - restart_dhcpd_RHEL
    - restart_isc-dhcp-server_DEB

# TODO: Verify the DHCP config has correct syntax
# dhcpd -t -cf /etc/dhcp/dhcpd.conf
# Working Output:
# Internet Systems Consortium DHCP Server 4.4.3-P1
# Copyright 2004-2022 Internet Systems Consortium.
# All rights reserved.
# For info, please visit https://www.isc.org/software/dhcp/
# Config file: /etc/dhcp/dhcpd.conf
# Database file: /var/lib/dhcp/dhcpd.leases
# PID file: /var/run/dhcpd.pid
