---
# tasks file for dhcpd

# - name: Import task nfs_mounts
#   import_tasks: nfs_mounts.yaml

# - name: Import task containers
#   import_tasks: containers.yaml

- name: setup_system_dhcpd
  import_tasks: sysDHCP.yaml

- name: copy_dhcpd_configs
  import_tasks: dhcpdConfigs.yaml

# - name: start_dhcpd_service_RHEL
#   ansible.builtin.service:
#     name: dhcpd
#     enabled: true
#     state: started
#   when: ansible_facts['os_family']|lower == 'rocky'
#         or ansible_facts['os_family']|lower == 'centos'
#         or ansible_facts['os_family']|lower == 'redhat'

# - name: start_dhcpd_service_DEB
#   ansible.builtin.service:
#     name: isc-dhcp-server
#     state: present
#   when: ansible_facts['os_family']|lower == 'debian'
