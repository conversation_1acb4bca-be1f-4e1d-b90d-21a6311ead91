---
- name: ensure_dhcpd_server_present_RHEL
  ansible.builtin.yum:
    name: dhcp-server
    state: present
  when: ansible_facts['os_family']|lower == 'rocky'
        or ansible_facts['os_family']|lower == 'centos'
        or ansible_facts['os_family']|lower == 'redhat'

- name: ensure_dhcpd_server_present_DEB
  ansible.builtin.apt:
    name: isc-dhcp-server
    state: present
  when: ansible_facts['os_family']|lower == 'debian'

- name: disable_dhcpcd_DEB
  ansible.builtin.systemd:
    name: dhcpcd
    state: stopped
    enabled: no
  when: ansible_facts['os_family']|lower == 'debian'

# - name: allow_ports_via_firewalld
#   ansible.posix.firewalld:
#     port: "{{ item.port }}"
#     permanent: yes
#     state: enabled
#   loop:
#     - port: 67-68/udp # BOOTPS/BOOTPC
#     - port: 647/tcp # DHCP Failover Primary TCP
#     - port: 847/tcp # DHCP Failover Secondary TCP
#     - port: 7911/tcp # OMAPI control port TCP
#   notify: Reload firewalld

- name: allow_dhcp_ports_iptables
  ansible.builtin.iptables:
      chain: INPUT
      protocol: "{{ item.protocol }}"
      source_port: "{{ item.port }}"
      destination_port: "{{ item.port }}"
      jump: ACCEPT
      comment: "ALLOW {{ item.comment }}"
  loop:
    - port: 67
      protocol: "udp"
      comment: "BOOTPS UDP"
    - port: 68
      protocol: "udp"
      comment: "BOOTPC UDP"
    - port: 647
      protocol: "tcp"
      comment: "DHCP Failover Primary TCP"
    - port: 847
      protocol: "tcp"
      comment: "DHCP Failover Secondary TCP"
    - port: 7911
      protocol: "tcp"
      comment: "OMAPI control port TCP"

- name: set_static_ip_primary
  community.general.nmcli:
    conn_name: eth0
    ifname: eth0
    type: ethernet
    ip4: *************/24
    gw4: ************
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-001")

- name: set_static_ip_secondary
  community.general.nmcli:
    conn_name: eth0
    ifname: eth0
    type: ethernet
    ip4: *************/24
    gw4: ************
    state: present
  when: (ansible_facts['hostname']|lower == "silmarillion-002")

# Space separated interface name list
- name: concatenate_ints_list_v4
  set_fact:
    dhcpd_v4_ints_str: "{{ dhcpd_v4_ints_list | join(' ') }}"

- name: concatenate_ints_list_v6
  set_fact:
    dhcpd_v6_ints_str: "{{ dhcpd_v6_ints_list | join(' ') }}"

- name: set_listening_int_RHEL
  ansible.builtin.copy:
    remote_src: yes
    src: "/usr/lib/systemd/system/dhcpd.service"
    dest: "{{ RHEL_dhcpd_service_file }}"
    mode:  '0755'
  notify: restart_dhcpd_RHEL
  when: ansible_facts['os_family']|lower == 'rocky'
        or ansible_facts['os_family']|lower == 'centos'
        or ansible_facts['os_family']|lower == 'redhat'

- name: set_v4_listening_interfaces_RHEL
  ansible.builtin.replace:
    path: "{{ RHEL_dhcpd_service_file }}"
    regexp: '(^ExecStart.*DHCPDARGS)'
    replace: '\1 {{ dhcpd_v4_ints_str }}'
  notify: restart_dhcpd_RHEL
  when: ansible_facts['os_family']|lower == 'rocky'
        or ansible_facts['os_family']|lower == 'centos'
        or ansible_facts['os_family']|lower == 'redhat'

- name: set_v4_listening_interfaces_DEB
  ansible.builtin.replace:
    path: "{{ DEB_dhcpd_service_file }}"
    regexp: '(^INTERFACESv4=)"(.*)"$'
    replace: '\1"{{ dhcpd_v4_ints_str }}"'
  notify: restart_isc-dhcp-server_DEB
  when: ansible_facts['os_family']|lower == 'debian'

# - name: set_v6_listening_interfaces_DEB
#   ansible.builtin.replace:
#     path: "{{ DEB_dhcpd_service_file }}"
#     regexp: '(^INTERFACESv6=)"(.*)"$'
#     replace: '\1"{{ dhcpd_v6_ints_str }}"'
#   notify: restart_isc-dhcp-server_DEB
#   when: ansible_facts['os_family']|lower == 'debian'
