---
# handlers file for dhcpd
- name: restart_dhcpd_RHEL
  ansible.builtin.systemd:
    name: dhcpd
    state: restarted
    enabled: yes
  when: ansible_facts['os_family']|lower == 'rocky'
      or ansible_facts['os_family']|lower == 'centos'
      or ansible_facts['os_family']|lower == 'redhat'

- name: restart_isc-dhcp-server_DEB
  ansible.builtin.systemd:
    name: isc-dhcp-server
    state: restarted
    enabled: yes
  when: ansible_facts['os_family']|lower == 'debian'
