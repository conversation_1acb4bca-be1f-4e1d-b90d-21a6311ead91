# Failover based on RFC https://www.ietf.org/proceedings/59/I-D/draft-ietf-dhc-failover-12.txt
omapi-port 7911;
omapi-key omapi_key;

key omapi_key {
    algorithm hmac-md5;
    secret RJDF2JXjEgIEeYDuJCWuXVjq6hXIF3AdzjDjdS+wOULuCKBBco6M3jgmOozEHb/vMzIba++jY5gI6audT9TBXA==;
}

failover peer "dhcp-failover" {
    primary;
    address *************;
    peer address *************;
    port 647;
    peer port 847;
    max-response-delay 60;
    max-unacked-updates 10;
    load balance max seconds 3;
    mclt 3600;
    split 128;
}

authoritative;
log-facility local7;
default-lease-time 28800; # 8 hours
max-lease-time 86400; # 1 day

include "/etc/dhcp/zones/guest/dhcpd.conf";
include "/etc/dhcp/zones/guest/static.conf";
include "/etc/dhcp/zones/management/dhcpd.conf";
include "/etc/dhcp/zones/management/static.conf";
include "/etc/dhcp/zones/security/dhcpd.conf";
include "/etc/dhcp/zones/security/static.conf";
include "/etc/dhcp/zones/trust/dhcpd.conf";
include "/etc/dhcp/zones/trust/static.conf";
include "/etc/dhcp/zones/piavpn/dhcpd.conf";
include "/etc/dhcp/zones/piavpn/static.conf";
include "/etc/dhcp/zones/iot/dhcpd.conf";
include "/etc/dhcp/zones/iot/static.conf";
