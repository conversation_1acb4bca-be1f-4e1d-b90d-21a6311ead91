host og01-a01-<PERSON><PERSON><PERSON><PERSON> {
    hardware ethernet 00:13:c6:01:a5:6e;
    fixed-address *************;
}

host printer-MC362-wireless {
    hardware ethernet 00:25:36:36:a5:02;
    fixed-address *************;
}

# Brother Color Printer
host printer-hll3270cdw-wireless {
    hardware ethernet cc:6b:1e:8f:36:65;
    fixed-address *************;
}

host epson-scanner-rr-600w {
    hardware ethernet dc:cd:2f:f3:64:0f;
    fixed-address *************;
}

# xen-orchestra staticly set
host gandalf {
    hardware ethernet 42:2b:68:f2:30:cd;
    fixed-address *************;
}

# pi-001 staticly set
host silmarillian-001 {
    hardware ethernet dc:a6:32:e7:5d:77;
    fixed-address *************;
}

# pi-002 staticly set
host silmarillian-002 {
    hardware ethernet dc:a6:32:e7:5d:d1;
    fixed-address *************;
}

# pi-003 staticly set
host silmarillian-003 {
    hardware ethernet dc:a6:32:e9:54:2f;
    fixed-address *************;
}

host blueberry<PERSON> {
    hardware ethernet dc:a6:32:e9:53:e1;
    fixed-address *************;
}

host homeassistant {
    hardware ethernet 00:1e:06:45:3a:44;
    fixed-address *************;
}

host retro-games {
    hardware ethernet e8:ff:1e:d0:39:cc;
    fixed-address *************;
}

host docker-host-001 {
    hardware ethernet 06:40:1d:7a:f4:b1;
    fixed-address *************;
}

# NAS staticly set
host nasa {
    hardware ethernet f4:52:14:80:ab:60;
    fixed-address *************;
}

host xen-orchestra-001 {
    hardware ethernet 22:06:b3:6a:f6:b2;
    fixed-address *************;
}

host WinSrv2019-trust {
    hardware ethernet 00:0c:29:d2:25:a8;
    fixed-address *************;
}

host bombadil {
    hardware ethernet 00:0c:29:22:11:8b;
    fixed-address *************;
}

host plex {
    hardware ethernet 00:e0:4c:01:98:9c;
    fixed-address *************;
}

host Win-NVR-Trust {
    hardware ethernet 6a:4e:02:8a:b6:c0;
    fixed-address *************;
}

host Linux-NVR-Trust {
    hardware ethernet 7a:26:6d:d9:3f:87;
    fixed-address *************;
}

host merry {
    hardware ethernet 46:76:31:b9:d3:3a;
    fixed-address *************;
}

host pippin {
    hardware ethernet a2:f5:2e:c4:8b:12;
    fixed-address *************;
}

host ospi {
    hardware ethernet b8:27:eb:a5:89:54;
    fixed-address *************;
}

host octopi {
    hardware ethernet dc:a6:32:0e:e2:ab;
    fixed-address *************;
}

host status-cube-eth {
    hardware ethernet b8:27:eb:1d:e8:b5;
    fixed-address *************;
}

host status-cube-wifi {
    hardware ethernet 74:da:38:2b:03:22;
    fixed-address *************;
}

host BeagleBoneBlack {
    hardware ethernet a8:10:87:b3:c0:57;
    fixed-address *************;
}

host ratago-001 {
    hardware ethernet c4:d8:d5:0c:d3:16;
    fixed-address *************;
}

host ratago-002 {
    hardware ethernet c4:d8:d5:0b:0c:13;
    fixed-address *************;
}

host ratago-003 {
    hardware ethernet c4:d8:d5:0b:08:ab;
    fixed-address *************;
}

host lilly-esp32-atom-echo {
    hardware ethernet 10:06:1c:0a:c2:1c;
    fixed-address *************;
}

host florence-esp32-bed-lights {
    hardware ethernet 24:62:ab:fc:ff:8c;
    fixed-address *************;
}

host florence-esp32-atom-echo {
    hardware ethernet 10:06:1c:0a:ee:74;
    fixed-address *************;
}

host krizzo-linux-vm {
    hardware ethernet ca:21:3f:30:76:1f;
    fixed-address *************7;
}

host krizzo-arch-desktop {
    hardware ethernet f0:92:1c:e8:e6:8b;
    fixed-address *************8;
}

host krizzo-win10-desktop {
    hardware ethernet 00:d8:61:1a:30:b2;
    fixed-address *************9;
}

host krizzo-w530-laptop-wired {
    hardware ethernet 3c:97:0e:ba:03:e9;
    fixed-address *************0;
}

host Anker-DarkGray-USBC-adaptor {
    hardware ethernet 00:e0:4c:02:14:04;
    fixed-address *************1;
}

host pixelxl-phone {
    hardware ethernet ac:37:43:df:1b:d4;
    fixed-address *************2;
}

host pixel3a-srizzo-phone {
    hardware ethernet 58:cb:52:46:31:fc;
    fixed-address *************3;
}

host pixel5-krizzo-phone {
    hardware ethernet 6e:d9:a5:6f:80:bc;
    fixed-address *************4;
}

host lilly-desktop-wired {
    hardware ethernet 98:fa:9b:b7:1c:93;
    fixed-address *************6;
}

host lilly-desktop-wifi {
    hardware ethernet e8:4e:06:63:f3:55;
    fixed-address *************7;
}

host tyler-desktop-wired {
    hardware ethernet 98:fa:9b:b7:18:14;
    fixed-address *************8;
}

host tyler-desktop-wifi {
    hardware ethernet e8:4e:06:63:f3:86;
    fixed-address *************9;
}

host nintendo-switch-001 {
    hardware ethernet b8:8a:ec:7d:e5:3b;
    fixed-address *************0;
}

host nintendo-switch-002 {
    hardware ethernet 74:f9:ca:06:a2:58;
    fixed-address *************1;
}

host krizzo-w530-laptop-wireless {
    hardware ethernet 3c:a9:f4:34:a3:d0;
    fixed-address *************2;
}

host samantha-win10-vm {
    hardware ethernet 92:41:4f:9d:5d:0d;
    fixed-address **************;
}

host WIN10-SOLIDWORKS {
    hardware ethernet 10:60:4b:64:ca:39;
    fixed-address **************;
}

host lilly-linux {
    hardware ethernet 66:2d:f0:7d:d7:0a;
    fixed-address **************;
}

host deb-001 {
    hardware ethernet 00:00:00:00:10:01;
    fixed-address **************;
}

host deb-002 {
    hardware ethernet 00:00:00:00:10:02;
    fixed-address **************;
}

host deb-003 {
    hardware ethernet 00:00:00:00:10:03;
    fixed-address **************;
}

host alma-001 {
    hardware ethernet 52:71:66:32:af:5d;
    fixed-address **************;
}
