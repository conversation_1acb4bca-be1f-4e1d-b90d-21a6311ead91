subnet 192.168.28.0 netmask 255.255.255.0 {
    option subnet-mask 255.255.255.0;
    option routers 192.168.28.1;
    option domain-name-servers 1.1.1.1, 8.8.8.8;
    default-lease-time 28800; # 8 hours
    max-lease-time 86400; # 1 day
    pool {
        failover peer "dhcp-failover";
        range 192.168.28.200 192.168.28.254;
        default-lease-time 28800; # 8 hours
        max-lease-time 86400; # 1 day
    }
    next-server 10.10.10.10;
    if option  architecture-type = 00:07 {
      filename "grubx64.efi";
    } else {
      filename "pxelinux.0";
    }
}
