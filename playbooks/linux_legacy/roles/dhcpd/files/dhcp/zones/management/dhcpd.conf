option space ubnt;
option ubnt.unifi-address code 1 = ip-address;
class "ubnt" {
    match if substring (option vendor-class-identifier, 0, 4) = "ubnt";
    option vendor-class-identifier "ubnt";
    vendor-option-space ubnt;
    option ubnt.unifi-address **********; ### UniFi Controller IP ###
}

subnet ************ netmask ************* {
    option subnet-mask *************;
    option routers ************;
    option domain-name-servers *******, *******;
    default-lease-time 3600; # 1 hour
    max-lease-time 3600; # 1 hour

    pool {
        failover peer "dhcp-failover";
        range ************** **************;
        default-lease-time 3600; # 1 hour
        max-lease-time 3600; # 1 hour
    }

    # Set a bootstrap file based on it being MACADDR.cfg
    # See https://kb.isc.org/docs/aa-01039 for why each MAC section needs to be procssed due to leading 0's
    # option bootfile-name = concat (
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,1,1))),2), ":",
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,2,1))),2), ":",
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,3,1))),2), ":",
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,4,1))),2), ":",
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,5,1))),2), ":",
    # suffix (concat ("0", binary-to-ascii (16, 8, "", substring(hardware,6,1))),2)
    # );
}
