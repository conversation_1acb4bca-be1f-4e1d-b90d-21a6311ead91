---
# defaults file for dhcpd

provider:
  server: "{{ ansible_host }}"
  user: "{{ username }}"
  password: "{{ password }}"
  server_port: "{{ ansible_port }}"

dhcp_confg_src: "../files/dhcp/"
dhcpd_v4_ints_list:
  - "eth0"
  # - "br0"
dhcpd_v6_ints_list:
  - "eth0"
  # - "br0"

primary_dhcpd_conf: "dhcpd_primary.conf"
secondary_dhcpd_conf: "dhcpd_secondary.conf"

dhcpd_conf_dst: "/etc/dhcp/"
dhcpd_conf_filename: "dhcpd.conf"
RHEL_dhcpd_service_file: "/etc/systemd/system/dhcpd.service"
DEB_dhcpd_service_file: "/etc/default/isc-dhcp-server"

RHEL_pkgs:

DEB_pkgs:
