---
- name: start_nautobot_container_DEB
  community.docker.docker_container:
    name: nautobot
    image: "nautobotinc/pms-docker:nautobotpass"
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - 32400:32400/tcp
      - 3005:3005/tcp
      - 8324:8324/tcp
      - 32469:32469/tcp
      - 1900:1900/udp
      - 32410:32410/udp
      - 32412:32412/udp
      - 32413:32413/udp
      - 32414:32414/udp
    shm_size: 16G
    volumes:
      - /mnt/nasa-p1z2-docker-data-nautobot-config:/config
      # - /dev/shm:/transcode # Offload transcoding to RAM, requires a lot of memory on the VM
      - /mnt/nasa-p1z2-media:/data
    env:
      TZ: "America/Denver"
      PLEX_CLAIM: "<claimToken>" # Found at https://www.nautobot.tv/claim
      ADVERTISE_IP: "http://*************:32400/" # Only needed in bridge networking mode
      HOSTNAME: "nautobot.krizzo.io"
      PLEX_UID: "1000"
      PLEX_GID: "1000"

- name: start_tautulli_container_DEB
  community.docker.docker_container:
    name: tautulli
    image: ghcr.io/linuxserver/tautulli
    state: started
    restart: yes
    restart_policy: unless-stopped
    pull: yes
    ports:
      - "8181:8181"
    volumes:
      - /mnt/nasa-p1z2-docker-data-tautulli-config:/config
      - /mnt/nasa-p1z2-docker-data-nautobot-config/Library/Application Support/Plex Media Server/Logs:/logs,ro
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
