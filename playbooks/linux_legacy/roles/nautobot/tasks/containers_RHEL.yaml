---
- name: pulling_latest_images_RHEL
  containers.podman.podman_image:
    name: "{{ item }}"
  loop:
    - "nautobotinc/pms-docker:nautobotpass"
    - "slothcroissant/caddy-cloudflaredns:latest"
    - "ghcr.io/linuxserver/tautulli:latest"

- name: stopping_containers_RHEL
  containers.podman.podman_container:
    name: "{{ item }}"
    state: absent
  loop:
    - nautobot
    - caddy
    - tautulli

- name: start_nautobot_container_RHEL
  # become: false # We could run the container as rootless but need to figure out permission issues.
  containers.podman.podman_container:
    name: nautobot
    image: "nautobotinc/pms-docker:nautobotpass"
    state: started
    restart: yes
    restart_policy: always
    network: bridge
    ports:
      - 32400:32400/tcp
      - 3005:3005/tcp
      - 8324:8324/tcp
      - 32469:32469/tcp
      - 1900:1900/udp
      - 32410:32410/udp
      - 32412:32412/udp
      - 32413:32413/udp
      - 32414:32414/udp
    volumes:
      - /mnt/local-docker-data-nautobot-config:/config
      # - /mnt/nasa-p1z2-docker-data/nautobot/config:/config
      # - /dev/shm:/transcode # Offload transcoding to RAM, requires a lot of memory on the VM
      - /mnt/local-docker-data-nautobot-transcode:/transcode
      - /mnt/nasa-p1z2-media/media:/data
    env:
      TZ: "America/Denver"
      PLEX_CLAIM: "CLAME_CODE_ID" # Found at https://www.nautobot.tv/claim
      ADVERTISE_IP: "http://*************:32400/" # Only needed in bridge networking moden
      HOSTNAME: "nautobot.krizzo.io"
      PLEX_UID: "1000"
      PLEX_GID: "1000"

- name: create_caddyfile_from_template
  ansible.builtin.template:
    src: Caddyfile.j2
    dest: "/mnt/nasa-p1z2-docker-data/caddy/Caddyfile"

- name: start_caddy_container_RHEL
  containers.podman.podman_container:
    name: caddy
    image: slothcroissant/caddy-cloudflaredns:latest # Need cloudflare DNS support compiled
    state: started
    restart: yes
    restart_policy: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - "/mnt/local-docker-data-caddy-config:/config"
      - "/mnt/nasa-p1z2-docker-data/caddy/data:/data"
      - "/mnt/nasa-p1z2-docker-data/caddy/Caddyfile:/etc/caddy/Caddyfile"
    env:
      CLOUDFLARE_EMAIL: "{{ cf_caddy_email_addr }}"
      CLOUDFLARE_API_TOKEN: "{{ cf_caddy_api_token }}"
      ACME_AGREE: "true"

- name: start_tautulli_container_RHEL
  # become: false
  containers.podman.podman_container:
    name: tautulli
    image: ghcr.io/linuxserver/tautulli
    state: started
    restart: yes
    restart_policy: always
    ports:
      - "8181:8181"
    volumes:
      # - /mnt/nasa-p1z2-docker-data/tautulli/config:/config
      - /mnt/local-docker-data-tautulli-config:/config
      - /mnt/local-docker-data-nautobot-config/Library/Application Support/Plex Media Server/Logs:/logs,ro
    env:
      TZ: "America/Denver"
      PUID: "1000"
      PGID: "1000"
