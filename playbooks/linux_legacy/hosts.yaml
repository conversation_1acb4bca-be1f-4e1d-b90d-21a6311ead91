# vim: set syntax=yaml:
---
all:
  hosts:
  children:
    linuxServers:
      children:
        DEBServers:
        RELServers:
    DEBServers:
      vars:
        # Because ansible fails due to maintaining a list of distros and paths to python.
        ansible_python_interpreter: "/usr/bin/python3"
      hosts:
        silmarillion-003: # Network Services bind9 and pihole, unifi controller
          ansible_host: *************
        blueberrypi:
          ansible_host: *************
        merry: # Minecraft Server
          ansible_host: *************
        retro-games: # Emulation computer
          ansible_host: *************
      children:
        dhcpServers:
        debTestSerers:
    RELServers:
      vars:
        # Because ansible fails due to maintaining a list of distros and paths to python.
        # Deb hosts need sudo installed and user to sudo access
        # ansible_python_interpreter: "/usr/bin/python3"
      hosts:
        plex:
          ansible_host: *************
        pippin: # Sourcer
          ansible_host: *************
        gandalf: #xenOrchestra
          ansible_host: *************
    dhcpServers:
      hosts:
        silmarillion-001: # Network Services
          ansible_host: *************
        silmarillion-002: # Network Services
          ansible_host: *************
    testServers:
      children:
        debTestSerers:
    debTestSerers:
      hosts:
        deb-001:
          ansible_host: **************
        deb-002:
          ansible_host: **************
        deb-003:
          ansible_host: **************
