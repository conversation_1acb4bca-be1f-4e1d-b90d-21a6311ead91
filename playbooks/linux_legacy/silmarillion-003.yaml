---
# Run:
#   CWD: ansible/playbooks/linux_legacy/
#     ansible-playbook -l silmarillion-003 silmarillion-003.yaml --check
#   Update Containers:
#     ansible-playbook -l silmarillion-003 silmarillion-003.yaml --start-at-task="cfddns : start_cf_ddns"
#   Update dashy config:
#     ansible-playbook -l silmarillion-003 silmarillion-003.yaml --start-at-task="dashy : copy_dashy_config"

- name: setup_silmarillion_003_server
  become: true
  gather_facts: true
  hosts: silmarillion-003

  # vars_prompt:
  #   - name: "cf_ddns_api_key"
  #     prompt: "Cloud Flare API Key"
  #     private: yes
  #     confirm: yes

  tasks:
     - name: configure_base_setup_role
       import_role:
         name: base-setup

     - name: configure_docker_host_role
       import_role:
         name: docker-host
       when: ansible_facts['os_family']|lower == 'debian'

  #   - name: configure_The_Lounge_IRC_role
  #     import_role:
  #       name: TheLoungeIRC

    #  - name: setup_cf_ddns_services
    #    import_role:
    #      name: cfddns

     - name: configure_unifi_controller_role
       import_role:
         name: unifi-controller

     - name: configure_dashy_role
       import_role:
         name: dashy

     - name: configure_pihole_role
       import_role:
         name: pihole
