---
# tasks file for base-setup

- name: INFO ==> system info
  ansible.builtin.debug:
    msg: "OS_Family: {{ ansible_facts['os_family']|lower }}\nVirt_Role: {{ ansible_facts['virtualization_role'] }}\nVirt_Type: {{ ansible_facts['virtualization_type'] }}"
    verbosity: 1

- name: Setup DEB base system
  import_tasks: deb.yaml
  when: ansible_facts['os_family']|lower == 'debian'

# - name: Setup RHEL base system
#   import_tasks: rhel.yaml
#   when: ansible_facts['os_family']|lower == 'rocky'
#     or ansible_facts['os_family']|lower == 'centos'
#     or ansible_facts['os_family']|lower == 'redhat'

- name: Configure users
  include_tasks: config_users.yaml
  loop: "{{ users_list }}"
  loop_control:
    loop_var: user_info



# - name: install_vm_guest_tools
#   import_tasks: guest_tools.yaml
#   when: ansible_facts['virtualization_role']|lower == 'guest'
#         and (
#           ansible_facts['os_family']|lower == 'rocky'
#           or ansible_facts['os_family']|lower == 'centos'
#           or ansible_facts['os_family']|lower == 'redhat')
#           and ansible_facts['virtualization_type']|lower == 'xen'
#           )

# - name: setup_user
#   import_tasks: set_user.yaml # Debian is already setup on pis

# Works on a fresh insatll but fails once it successes the first time. Possibly due to python 3.9 being installed.
# An exception occurred during task execution. To see the full traceback, use -vvv. The error was: ModuleNotFoundError: No module named 'selinux'
# fatal: [rts-001]: FAILED! => {
#     "changed": false
# }
#
# MSG:
#
# Failed to import the required Python library (libselinux-python) on rts.krizzo.io's Python /usr/bin/python3.9.
# Please read the module documentation and install # it in the appropriate location.
# If the required library is installed, but Ansible is using the wrong Python interpreter, please consult the documentation on ansible_python_interpreter
# - name: selinux_setup
#   import_tasks: rel_selinux.yaml
#   when: ansible_facts['os_family']|lower == 'rocky'
#     or ansible_facts['os_family']|lower == 'centos'
#     or ansible_facts['os_family']|lower == 'redhat'
