---

- name: Install REL packages
  ansible.builtin.dnf:
    name: "{{ RHEL_pkgs }}"
    state: latest
    update_cache: yes

- name: Install python packages
  become: false
  ansible.builtin.pip:
    executable: pip3
    name: "{{ RHEL_pip_pkgs }}"
    state: latest

# Install XE VM guest Tools
- name: Install VM XE guest tools
  ansible.builtin.dnf:
    name: xe-guest-utilities-latest
    state: latest
    update_cache: yes
  when: ansible_facts['virtualization_type']|lower == 'xen'

- name: Enable VM XE guest tools
  ansible.builtin.systemd:
    name: xe-linux-distribution
    state: started
    enabled: yes
  when: ansible_facts['virtualization_type']|lower == 'xen'

# Works on a fresh install but fails once it successes the first time. Possibly due to python 3.9 being installed.
# An exception occurred during task execution. To see the full traceback, use -vvv. The error was: ModuleNotFoundError: No module named 'selinux'
# fatal: [rts-001]: FAILED! => {
#     "changed": false
# }
#
# MSG:
#
# Failed to import the required Python library (libselinux-python) on rts.krizzo.io's Python /usr/bin/python3.9.
# Please read the module documentation and install # it in the appropriate location.
# If the required library is installed, but Ansible is using the wrong Python interpreter, please consult the documentation on ansible_python_interpreter
# - name: Permissive SE Linux
#   import_tasks: rhel_selinux.yaml
