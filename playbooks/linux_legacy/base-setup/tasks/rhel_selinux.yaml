---

# If your local python interpreter doesn't have the selinux module you'll get the following error.
# Fix with `pip install selinux` or `yay -S selinux-python` on the host not remote node.

# An exception occurred during task execution. To see the full traceback, use -vvv. The error was: ModuleNotFoundError: No module named 'selinux'
# fatal: [rts-001]: FAILED! => {
#     "changed": false
# }
#
# MSG:
#
# Failed to import the required Python library (libselinux-python) on rts.krizzo.io's Python /usr/bin/python3.9. \
# Please read the module documentation and install it in the appropriate location. If the required library is installed, \
# but Ansible is using the wrong Python interpreter, please consult the documentation on ansible_python_interpreter

- name: Set SElinux to permissive
  ansible.posix.selinux:
    policy: targeted
    state: permissive
  notify: Reboot host and wait for it to restart

- name: Reboot if necessary
  meta: flush_handlers
