---

- name: Get available groups
  ansible.builtin.getent:
    database: group

- name: Get available Users
  ansible.builtin.getent:
    database: passwd

- name: "Create {{ user_info.name }} user and group"
  block:
    - name: "Create group {{ user_info.name }} with {{ user_info.id }}"
      ansible.builtin.group:
        name: "{{ user_info.name }}"
        gid: "{{ user_info.id }}"
      when:
        - "user_info.name not in ansible_facts.getent_group"
        - "user_info.id not in item.value"
      loop: "{{ ansible_facts.getent_group | dict2items }}"

    - name: "Create user {{ user_info.name }} with {{ user_info.id }}"
      ansible.builtin.user:
        name: "{{ user_info.name }}"
        group: "{{ user_info.name }}" # Primary Group
        shell: "{{ user_info.shell }}"
        uid: "{{ user_info.id }}"
        create_home: yes
      when:
        - "user_info.name not in ansible_facts.getent_passwd" # Only create the user if they don't exist
        - "user_info.id not in item.value[1]"
      loop: "{{ ansible_facts.getent_passwd | dict2items }}"

  # rescue: # if the ID already exists lets create a random number
  #   - name: Create random number between 2000-2500 for group id
  #     ansible.builtin.set_fact:
  #       random_num: "{{ range(2000, 2500) | random(seed=item) }}"
  #     run_once: true
  #     with_items:
  #       - string

  #   - name: "Create group {{ user_info.name }} with gid {{ random_num }} when not available"
  #     ansible.builtin.group:
  #       name: "{{ user_info.name }}"
  #       gid: "{{ random_num }}"
  #     when:
  #       - "user_info.name not in ansible_facts.getent_group"
  #       - "random_num not in item.value"
  #     loop: "{{ ansible_facts.getent_group | dict2items }}"

- name: Setup ssh access for user
  block:
    - name: Create ~/.ssh dir for {{ user_info.name }}
      ansible.builtin.file:
        path: "~/.ssh"
        state: directory
        mode: "0700"
        owner: "{{ user_info.name }}"
        group: "{{ user_info.name }}"

    - name: Set user SSH authorized keys
      ansible.posix.authorized_key:
        user: "{{ user_info.name }}"
        key: "{{ ssh_key }}"
        state: present
      loop: "{{ user_info.ssh_pub_keys }}"
      loop_control:
        loop_var: ssh_key
  when:
    - "user_info.ssh"

- name: "Create /etc/sudoers.d/{{ user_info.name }} file"
  become: true
  ansible.builtin.lineinfile:
    path: "{{ sudoers_path }}/{{ user_info.name }}"
    line: "{{ user_info.name }} {{ user_info.sudo_permission }}"
    create: yes
    owner: root
    group: root
    mode: "0440"
    state: present
    validate: "/usr/sbin/visudo -c -f %s"
  when:
    - "user_info.sudoer"
