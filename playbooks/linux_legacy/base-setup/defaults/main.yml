---
# defaults file for base-setup

provider:
  server: "{{ ansible_host }}"
  user: "{{ username }}"
  password: "{{ password }}"
  server_port: "{{ ansible_port }}"

RHEL_pkgs:
  - epel-release
  - elrepo-release
  - kmod-wireguard
  - wireguard-tools
  - neovim
  # - bat
  - mtr
  - curl
  - zsh
  - tmux
  - jq
  - git
  - tcpdump
  - nfs-utils
  - dnf-utils
  - bind-utils
  - python39
  - python3-libselinux
  - NetworkManager
  - podman
  - rsync
  - ncdu

RHEL_pip_pkgs:
  - selinux

DEB_pkgs:
  - apt-file
  - neovim
  - bat
  - mtr
  - curl
  - zsh
  - tmux
  - jq
  - git
  - tcpdump
  - dnsutils
  - nfs-common
  - bind9utils
  - iptables
  - network-manager
  - rsync
  - ncdu
  - sudo
  - smbclient
  # - python3-pip

# DEB_pip_pkgs:
