---
#   Merry Install:
#     ansible-playbook -l merry minecraft.yaml
#   Update Containers:
#     ansible-playbook -l merry minecraft.yaml --start-at-task="minecraft : start_containers"

- name: Set up minecraft server
  become: true
  gather_facts: true
  hosts: merry

#  vars_prompt:
#    - name: "mc_rcon_password"
#      prompt: "RCON Password"
#      private: yes
#      confirm: yes

  tasks:
    # - name: setup_docker_service
    #   import_role:
    #     name: docker-host

    - name: setup_minecraft_services
      import_role:
        name: minecraft
