---
#   deluge Install:
#     ansible-playbook -l pippin deluge.yaml
#   Update Containers:
#     ansible-playbook -l pippin deluge.yaml --start-at-task="deluge : start_deluge_container"

- name: Set up torrent server
  become: true
  gather_facts: true
  hosts: pippin

  vars_prompt:
    - name: "pia_account_username"
      prompt: "[Deluge] PIA User Name"
      default: p1709977
      private: no

    - name: "pia_account_password"
      prompt: "[Deluge] PIA Password"
      private: yes
      default: wf5kj7CqNNKKcW9mAtNVVkZwE3Zxs9V2
      confirm: yes

  tasks:
    - name: setup_deluge_services
      import_role:
        name: deluge

    - name: setup_jackett_services
      import_role:
        name: jackett
