---
#   Plex Install:
#     ansible-playbook -l plex plex.yaml --check -v
#   Update Containers:
#     ansible-playbook plex.yaml --start-at-task="plex : start_plex_container"
#     ansible-playbook plex.yaml --start-at-task="plex : start_tautulli_container_RHEL"

- name: Set up plex server
  become: true
  gather_facts: true
  hosts: "{{ hosts | default('plex') }}"

  # vars_prompt:
  #   - name: "plex_url"
  #     prompt: "plex url"
  #     private: no
  #     default: "http://*************:32400"

  #   - name: "tautulli_url"
  #     prompt: "tautulli url"
  #     private: no
  #     default: "http://*************:8181"

  #   - name: "radarr_url"
  #     prompt: "radarr url"
  #     private: no
  #     default: "http://*************:7878"

  #   - name: "sonarr_url"
  #     prompt: "sonarr url"
  #     private: no
  #     default: "http://*************:8989"

  #   - name: "plex_meta_manager_data.tmdb.apikey"
  #     prompt: "tmdb api key"
  #     private: yes

  #   - name: "plex_meta_manager_data.plex.token"
  #     prompt: "plex token (api)"
  #     private: yes

  #   - name: "plex_meta_manager_data.tautulli.apikey"
  #     prompt: "tautulli api key"
  #     private: yes

  #   - name: "plex_meta_manager_data.radarr.token"
  #     prompt: "radarr api key"
  #     private: yes

  #   - name: "plex_meta_manager_data.sonarr.token"
  #     prompt: "sonarr api key"
  #     private: yes

  tasks:
    - name: setup_docker_service
      import_role:
        name: docker-host
      when: ansible_facts['os_family']|lower == 'debian'

    - name: setup_plex_services
      import_role:
        name: plex
