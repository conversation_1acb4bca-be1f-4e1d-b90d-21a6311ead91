# Ansible repo

## Check for any leaks

https://github.com/gitleaks/gitleaks

```bash
podman pull ghcr.io/gitleaks/gitleaks:latest
podman run -v ${PWD}:/path ghcr.io/gitleaks/gitleaks:latest detect --source="/path" -v

 ```

## Structure

- `linux-plays`: the main direct for setting up my linux hosts
- `arch_linux_arm_setup`: messing around with creating a bootable USB for a BBB possibly a PI and also setting it up after booted
- `arch_boot_setup`: should possibly be deleted and will be moved to the linux_plays dir
- `ansible_book`: is me following the oreilly ansible up and running book but I modified it for arch instead of debian, will probably go back to using debian as I want to test things out with it.

